('/home/<USER>/Documents/CodeRepository/file_extension_tool/dist/FileExtensionTool',
 False,
 False,
 False,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 None,
 None,
 None,
 '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/FileExtensionTool.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_struct.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/zlib.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('struct',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/venv/lib64/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('extension_tool',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/extension_tool.py',
   'PYSOURCE'),
  ('libpython3.13.so.1.0', '/lib64/libpython3.13.so.1.0', 'BINARY'),
  ('lib-dynload/_csv.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_csv.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_statistics.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_contextvars.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_decimal.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_hashlib.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha3.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_blake2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_md5.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha1.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_sha2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_random.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_bisect.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/math.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/array.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/select.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_socket.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/unicodedata.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/binascii.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/grp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/resource.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_opcode.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_pickle.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_heapq.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_multibytecodec.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_jp.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_kr.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_iso2022.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_cn.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_tw.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_bz2.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_codecs_hk.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_lzma.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_tkinter.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_json.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/_posixsubprocess.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-x86_64-linux-gnu.so',
   '/usr/lib64/python3.13/lib-dynload/fcntl.cpython-313-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libmpdec.so.3', '/lib64/libmpdec.so.3', 'BINARY'),
  ('libz.so.1', '/lib64/libz.so.1', 'BINARY'),
  ('libcrypto.so.3', '/lib64/libcrypto.so.3', 'BINARY'),
  ('libb2.so.1', '/lib64/libb2.so.1', 'BINARY'),
  ('libgomp.so.1', '/lib64/libgomp.so.1', 'BINARY'),
  ('libgcc_s.so.1', '/lib64/libgcc_s.so.1', 'BINARY'),
  ('libbz2.so.1', '/lib64/libbz2.so.1', 'BINARY'),
  ('liblzma.so.5', '/lib64/liblzma.so.5', 'BINARY'),
  ('libXau.so.6', '/lib64/libXau.so.6', 'BINARY'),
  ('libbrotlicommon.so.1', '/lib64/libbrotlicommon.so.1', 'BINARY'),
  ('libtcl8.6.so', '/lib64/libtcl8.6.so', 'BINARY'),
  ('libX11.so.6', '/lib64/libX11.so.6', 'BINARY'),
  ('libgraphite2.so.3', '/lib64/libgraphite2.so.3', 'BINARY'),
  ('libxml2.so.2', '/lib64/libxml2.so.2', 'BINARY'),
  ('libfreetype.so.6', '/lib64/libfreetype.so.6', 'BINARY'),
  ('libtk8.6.so', '/lib64/libtk8.6.so', 'BINARY'),
  ('libpng16.so.16', '/lib64/libpng16.so.16', 'BINARY'),
  ('libXrender.so.1', '/lib64/libXrender.so.1', 'BINARY'),
  ('libbrotlidec.so.1', '/lib64/libbrotlidec.so.1', 'BINARY'),
  ('libharfbuzz.so.0', '/lib64/libharfbuzz.so.0', 'BINARY'),
  ('libglib-2.0.so.0', '/lib64/libglib-2.0.so.0', 'BINARY'),
  ('libXft.so.2', '/lib64/libXft.so.2', 'BINARY'),
  ('libpcre2-8.so.0', '/lib64/libpcre2-8.so.0', 'BINARY'),
  ('libfontconfig.so.1', '/lib64/libfontconfig.so.1', 'BINARY'),
  ('_tcl_data/encoding/tis-620.enc',
   '/usr/share/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('_tcl_data/msgs/de.msg', '/usr/share/tcl8.6/msgs/de.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/usr/share/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tk_data/msgs/ru.msg', '/usr/share/tk8.6/msgs/ru.msg', 'DATA'),
  ('_tk_data/icons.tcl', '/usr/share/tk8.6/icons.tcl', 'DATA'),
  ('_tcl_data/msgs/fr_ch.msg', '/usr/share/tcl8.6/msgs/fr_ch.msg', 'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/usr/share/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl', '/usr/share/tk8.6/ttk/scrollbar.tcl', 'DATA'),
  ('_tcl_data/msgs/te_in.msg', '/usr/share/tcl8.6/msgs/te_in.msg', 'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/usr/share/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tk_data/scale.tcl', '/usr/share/tk8.6/scale.tcl', 'DATA'),
  ('_tcl_data/msgs/zh_hk.msg', '/usr/share/tcl8.6/msgs/zh_hk.msg', 'DATA'),
  ('_tcl_data/msgs/gv_gb.msg', '/usr/share/tcl8.6/msgs/gv_gb.msg', 'DATA'),
  ('_tcl_data/msgs/en_hk.msg', '/usr/share/tcl8.6/msgs/en_hk.msg', 'DATA'),
  ('_tcl_data/msgs/fa_ir.msg', '/usr/share/tcl8.6/msgs/fa_ir.msg', 'DATA'),
  ('_tcl_data/http1.0/http.tcl', '/usr/share/tcl8.6/http1.0/http.tcl', 'DATA'),
  ('_tk_data/clrpick.tcl', '/usr/share/tk8.6/clrpick.tcl', 'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/usr/share/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/usr/share/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tcl_data/msgs/pl.msg', '/usr/share/tcl8.6/msgs/pl.msg', 'DATA'),
  ('_tcl_data/msgs/es_ni.msg', '/usr/share/tcl8.6/msgs/es_ni.msg', 'DATA'),
  ('_tcl_data/msgs/nb.msg', '/usr/share/tcl8.6/msgs/nb.msg', 'DATA'),
  ('_tcl_data/msgs/es_do.msg', '/usr/share/tcl8.6/msgs/es_do.msg', 'DATA'),
  ('_tcl_data/msgs/en_za.msg', '/usr/share/tcl8.6/msgs/en_za.msg', 'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/usr/share/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl', '/usr/share/tk8.6/ttk/notebook.tcl', 'DATA'),
  ('_tk_data/obsolete.tcl', '/usr/share/tk8.6/obsolete.tcl', 'DATA'),
  ('tcl8/8.4/platform-1.0.19.tm',
   '/usr/share/tcl8/8.4/platform-1.0.19.tm',
   'DATA'),
  ('_tk_data/msgbox.tcl', '/usr/share/tk8.6/msgbox.tcl', 'DATA'),
  ('_tcl_data/msgs/ca.msg', '/usr/share/tcl8.6/msgs/ca.msg', 'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/usr/share/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg', '/usr/share/tcl8.6/msgs/de_be.msg', 'DATA'),
  ('_tk_data/ttk/cursors.tcl', '/usr/share/tk8.6/ttk/cursors.tcl', 'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/usr/share/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl', '/usr/share/tk8.6/ttk/spinbox.tcl', 'DATA'),
  ('_tcl_data/msgs/ar_jo.msg', '/usr/share/tcl8.6/msgs/ar_jo.msg', 'DATA'),
  ('_tcl_data/msgs/af.msg', '/usr/share/tcl8.6/msgs/af.msg', 'DATA'),
  ('_tcl_data/msgs/en_bw.msg', '/usr/share/tcl8.6/msgs/en_bw.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/usr/share/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/msgs/ms.msg', '/usr/share/tcl8.6/msgs/ms.msg', 'DATA'),
  ('_tk_data/ttk/xpTheme.tcl', '/usr/share/tk8.6/ttk/xpTheme.tcl', 'DATA'),
  ('_tcl_data/msgs/hi.msg', '/usr/share/tcl8.6/msgs/hi.msg', 'DATA'),
  ('_tk_data/megawidget.tcl', '/usr/share/tk8.6/megawidget.tcl', 'DATA'),
  ('_tk_data/button.tcl', '/usr/share/tk8.6/button.tcl', 'DATA'),
  ('_tcl_data/msgs/pt.msg', '/usr/share/tcl8.6/msgs/pt.msg', 'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/usr/share/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/usr/share/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tcl_data/msgs/sl.msg', '/usr/share/tcl8.6/msgs/sl.msg', 'DATA'),
  ('_tcl_data/msgs/da.msg', '/usr/share/tcl8.6/msgs/da.msg', 'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/usr/share/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg', '/usr/share/tcl8.6/msgs/fr_be.msg', 'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/usr/share/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/he.msg', '/usr/share/tcl8.6/msgs/he.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/usr/share/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tk_data/text.tcl', '/usr/share/tk8.6/text.tcl', 'DATA'),
  ('_tcl_data/msgs/fo.msg', '/usr/share/tcl8.6/msgs/fo.msg', 'DATA'),
  ('_tk_data/ttk/sizegrip.tcl', '/usr/share/tk8.6/ttk/sizegrip.tcl', 'DATA'),
  ('_tcl_data/msgs/es_gt.msg', '/usr/share/tcl8.6/msgs/es_gt.msg', 'DATA'),
  ('_tk_data/spinbox.tcl', '/usr/share/tk8.6/spinbox.tcl', 'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/usr/share/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/usr/share/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl', '/usr/share/tk8.6/ttk/treeview.tcl', 'DATA'),
  ('_tk_data/entry.tcl', '/usr/share/tk8.6/entry.tcl', 'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/usr/share/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg', '/usr/share/tcl8.6/msgs/es_ec.msg', 'DATA'),
  ('_tcl_data/msgs/en_ph.msg', '/usr/share/tcl8.6/msgs/en_ph.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/usr/share/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg', '/usr/share/tcl8.6/msgs/ko_kr.msg', 'DATA'),
  ('_tcl_data/msgs/fi.msg', '/usr/share/tcl8.6/msgs/fi.msg', 'DATA'),
  ('_tcl_data/msgs/hr.msg', '/usr/share/tcl8.6/msgs/hr.msg', 'DATA'),
  ('_tcl_data/msgs/is.msg', '/usr/share/tcl8.6/msgs/is.msg', 'DATA'),
  ('_tk_data/tk.tcl', '/usr/share/tk8.6/tk.tcl', 'DATA'),
  ('_tcl_data/encoding/macCentEuro.enc',
   '/usr/share/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tcl_data/msgs/eo.msg', '/usr/share/tcl8.6/msgs/eo.msg', 'DATA'),
  ('_tcl_data/parray.tcl', '/usr/share/tcl8.6/parray.tcl', 'DATA'),
  ('_tk_data/ttk/defaults.tcl', '/usr/share/tk8.6/ttk/defaults.tcl', 'DATA'),
  ('_tk_data/ttk/altTheme.tcl', '/usr/share/tk8.6/ttk/altTheme.tcl', 'DATA'),
  ('_tk_data/ttk/button.tcl', '/usr/share/tk8.6/ttk/button.tcl', 'DATA'),
  ('_tcl_data/msgs/es_co.msg', '/usr/share/tcl8.6/msgs/es_co.msg', 'DATA'),
  ('_tcl_data/msgs/bg.msg', '/usr/share/tcl8.6/msgs/bg.msg', 'DATA'),
  ('_tcl_data/msgs/ar_in.msg', '/usr/share/tcl8.6/msgs/ar_in.msg', 'DATA'),
  ('_tcl_data/msgs/ms_my.msg', '/usr/share/tcl8.6/msgs/ms_my.msg', 'DATA'),
  ('_tcl_data/msgs/it.msg', '/usr/share/tcl8.6/msgs/it.msg', 'DATA'),
  ('_tcl_data/msgs/fa_in.msg', '/usr/share/tcl8.6/msgs/fa_in.msg', 'DATA'),
  ('_tcl_data/msgs/fr_ca.msg', '/usr/share/tcl8.6/msgs/fr_ca.msg', 'DATA'),
  ('_tk_data/palette.tcl', '/usr/share/tk8.6/palette.tcl', 'DATA'),
  ('tcl8/8.5/tcltest-2.5.7.tm', '/usr/share/tcl8/8.5/tcltest-2.5.7.tm', 'DATA'),
  ('_tcl_data/msgs/en_au.msg', '/usr/share/tcl8.6/msgs/en_au.msg', 'DATA'),
  ('_tcl_data/msgs/lv.msg', '/usr/share/tcl8.6/msgs/lv.msg', 'DATA'),
  ('_tcl_data/msgs/gl_es.msg', '/usr/share/tcl8.6/msgs/gl_es.msg', 'DATA'),
  ('_tcl_data/msgs/en_gb.msg', '/usr/share/tcl8.6/msgs/en_gb.msg', 'DATA'),
  ('_tk_data/msgs/fi.msg', '/usr/share/tk8.6/msgs/fi.msg', 'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/usr/share/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tcl_data/msgs/vi.msg', '/usr/share/tcl8.6/msgs/vi.msg', 'DATA'),
  ('_tk_data/msgs/hu.msg', '/usr/share/tk8.6/msgs/hu.msg', 'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/usr/share/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg', '/usr/share/tcl8.6/msgs/en_zw.msg', 'DATA'),
  ('_tk_data/msgs/pt.msg', '/usr/share/tk8.6/msgs/pt.msg', 'DATA'),
  ('_tcl_data/msgs/sw.msg', '/usr/share/tcl8.6/msgs/sw.msg', 'DATA'),
  ('_tk_data/ttk/combobox.tcl', '/usr/share/tk8.6/ttk/combobox.tcl', 'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/usr/share/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/usr/share/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/usr/share/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg', '/usr/share/tcl8.6/msgs/nl_be.msg', 'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/usr/share/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('_tcl_data/word.tcl', '/usr/share/tcl8.6/word.tcl', 'DATA'),
  ('_tcl_data/msgs/ta_in.msg', '/usr/share/tcl8.6/msgs/ta_in.msg', 'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/usr/share/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tk_data/images/tai-ku.gif', '/usr/share/tk8.6/images/tai-ku.gif', 'DATA'),
  ('_tcl_data/msgs/bn.msg', '/usr/share/tcl8.6/msgs/bn.msg', 'DATA'),
  ('_tk_data/comdlg.tcl', '/usr/share/tk8.6/comdlg.tcl', 'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/usr/share/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tcl_data/msgs/sv.msg', '/usr/share/tcl8.6/msgs/sv.msg', 'DATA'),
  ('_tk_data/scrlbar.tcl', '/usr/share/tk8.6/scrlbar.tcl', 'DATA'),
  ('_tcl_data/msgs/ta.msg', '/usr/share/tcl8.6/msgs/ta.msg', 'DATA'),
  ('_tcl_data/msgs/sk.msg', '/usr/share/tcl8.6/msgs/sk.msg', 'DATA'),
  ('_tk_data/ttk/entry.tcl', '/usr/share/tk8.6/ttk/entry.tcl', 'DATA'),
  ('_tcl_data/msgs/kl_gl.msg', '/usr/share/tcl8.6/msgs/kl_gl.msg', 'DATA'),
  ('_tk_data/iconlist.tcl', '/usr/share/tk8.6/iconlist.tcl', 'DATA'),
  ('_tcl_data/msgs/uk.msg', '/usr/share/tcl8.6/msgs/uk.msg', 'DATA'),
  ('_tk_data/menu.tcl', '/usr/share/tk8.6/menu.tcl', 'DATA'),
  ('_tk_data/safetk.tcl', '/usr/share/tk8.6/safetk.tcl', 'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/usr/share/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/usr/share/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/msgs/id.msg', '/usr/share/tcl8.6/msgs/id.msg', 'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/usr/share/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tk_data/xmfbox.tcl', '/usr/share/tk8.6/xmfbox.tcl', 'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/usr/share/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('_tcl_data/package.tcl', '/usr/share/tcl8.6/package.tcl', 'DATA'),
  ('_tk_data/msgs/sv.msg', '/usr/share/tk8.6/msgs/sv.msg', 'DATA'),
  ('_tcl_data/msgs/id_id.msg', '/usr/share/tcl8.6/msgs/id_id.msg', 'DATA'),
  ('_tcl_data/init.tcl', '/usr/share/tcl8.6/init.tcl', 'DATA'),
  ('_tk_data/msgs/pl.msg', '/usr/share/tk8.6/msgs/pl.msg', 'DATA'),
  ('_tcl_data/msgs/kl.msg', '/usr/share/tcl8.6/msgs/kl.msg', 'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/usr/share/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/usr/share/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg', '/usr/share/tk8.6/msgs/en_gb.msg', 'DATA'),
  ('_tcl_data/msgs/zh.msg', '/usr/share/tcl8.6/msgs/zh.msg', 'DATA'),
  ('_tcl_data/msgs/es_cl.msg', '/usr/share/tcl8.6/msgs/es_cl.msg', 'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/usr/share/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tk_data/msgs/en.msg', '/usr/share/tk8.6/msgs/en.msg', 'DATA'),
  ('_tcl_data/tm.tcl', '/usr/share/tcl8.6/tm.tcl', 'DATA'),
  ('_tcl_data/msgs/tr.msg', '/usr/share/tcl8.6/msgs/tr.msg', 'DATA'),
  ('_tcl_data/msgs/sq.msg', '/usr/share/tcl8.6/msgs/sq.msg', 'DATA'),
  ('_tk_data/tkfbox.tcl', '/usr/share/tk8.6/tkfbox.tcl', 'DATA'),
  ('_tk_data/ttk/winTheme.tcl', '/usr/share/tk8.6/ttk/winTheme.tcl', 'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/usr/share/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/usr/share/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tcl_data/msgs/fr.msg', '/usr/share/tcl8.6/msgs/fr.msg', 'DATA'),
  ('_tcl_data/msgs/en_ca.msg', '/usr/share/tcl8.6/msgs/en_ca.msg', 'DATA'),
  ('_tk_data/choosedir.tcl', '/usr/share/tk8.6/choosedir.tcl', 'DATA'),
  ('_tcl_data/msgs/sr.msg', '/usr/share/tcl8.6/msgs/sr.msg', 'DATA'),
  ('_tk_data/ttk/progress.tcl', '/usr/share/tk8.6/ttk/progress.tcl', 'DATA'),
  ('_tk_data/images/logo100.gif',
   '/usr/share/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/msgs/cs.msg', '/usr/share/tcl8.6/msgs/cs.msg', 'DATA'),
  ('_tcl_data/msgs/lt.msg', '/usr/share/tcl8.6/msgs/lt.msg', 'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/usr/share/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tcl_data/history.tcl', '/usr/share/tcl8.6/history.tcl', 'DATA'),
  ('_tcl_data/msgs/ga_ie.msg', '/usr/share/tcl8.6/msgs/ga_ie.msg', 'DATA'),
  ('_tcl_data/msgs/el.msg', '/usr/share/tcl8.6/msgs/el.msg', 'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/usr/share/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/usr/share/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/usr/share/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tcl_data/msgs/ko.msg', '/usr/share/tcl8.6/msgs/ko.msg', 'DATA'),
  ('_tcl_data/msgs/ru_ua.msg', '/usr/share/tcl8.6/msgs/ru_ua.msg', 'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/usr/share/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tcl_data/auto.tcl', '/usr/share/tcl8.6/auto.tcl', 'DATA'),
  ('_tcl_data/msgs/ru.msg', '/usr/share/tcl8.6/msgs/ru.msg', 'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/usr/share/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/usr/share/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/usr/share/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/usr/share/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tcl_data/msgs/be.msg', '/usr/share/tcl8.6/msgs/be.msg', 'DATA'),
  ('_tk_data/images/logo64.gif', '/usr/share/tk8.6/images/logo64.gif', 'DATA'),
  ('_tcl_data/msgs/kw.msg', '/usr/share/tcl8.6/msgs/kw.msg', 'DATA'),
  ('_tcl_data/safe.tcl', '/usr/share/tcl8.6/safe.tcl', 'DATA'),
  ('_tcl_data/msgs/es_hn.msg', '/usr/share/tcl8.6/msgs/es_hn.msg', 'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/usr/share/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/usr/share/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/usr/share/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl', '/usr/share/tk8.6/ttk/clamTheme.tcl', 'DATA'),
  ('_tk_data/ttk/ttk.tcl', '/usr/share/tk8.6/ttk/ttk.tcl', 'DATA'),
  ('_tk_data/panedwindow.tcl', '/usr/share/tk8.6/panedwindow.tcl', 'DATA'),
  ('_tcl_data/msgs/zh_cn.msg', '/usr/share/tcl8.6/msgs/zh_cn.msg', 'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/usr/share/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/usr/share/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/msgs/gl.msg', '/usr/share/tcl8.6/msgs/gl.msg', 'DATA'),
  ('_tk_data/ttk/scale.tcl', '/usr/share/tk8.6/ttk/scale.tcl', 'DATA'),
  ('_tcl_data/msgs/fa.msg', '/usr/share/tcl8.6/msgs/fa.msg', 'DATA'),
  ('_tcl_data/tclAppInit.c', '/usr/share/tcl8.6/tclAppInit.c', 'DATA'),
  ('_tcl_data/msgs/zh_sg.msg', '/usr/share/tcl8.6/msgs/zh_sg.msg', 'DATA'),
  ('_tcl_data/msgs/es.msg', '/usr/share/tcl8.6/msgs/es.msg', 'DATA'),
  ('_tcl_data/msgs/ga.msg', '/usr/share/tcl8.6/msgs/ga.msg', 'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/usr/share/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tcl_data/msgs/th.msg', '/usr/share/tcl8.6/msgs/th.msg', 'DATA'),
  ('_tcl_data/msgs/ar.msg', '/usr/share/tcl8.6/msgs/ar.msg', 'DATA'),
  ('_tcl_data/msgs/en_nz.msg', '/usr/share/tcl8.6/msgs/en_nz.msg', 'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/usr/share/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/usr/share/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('_tk_data/tearoff.tcl', '/usr/share/tk8.6/tearoff.tcl', 'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/usr/share/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tcl_data/msgs/nl.msg', '/usr/share/tcl8.6/msgs/nl.msg', 'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/usr/share/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tk_data/msgs/zh_cn.msg', '/usr/share/tk8.6/msgs/zh_cn.msg', 'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/usr/share/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg', '/usr/share/tcl8.6/msgs/es_ve.msg', 'DATA'),
  ('_tcl_data/msgs/et.msg', '/usr/share/tcl8.6/msgs/et.msg', 'DATA'),
  ('_tk_data/msgs/eo.msg', '/usr/share/tk8.6/msgs/eo.msg', 'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/usr/share/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/clock.tcl', '/usr/share/tcl8.6/clock.tcl', 'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/usr/share/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg', '/usr/share/tcl8.6/msgs/es_ar.msg', 'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/usr/share/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/usr/share/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/usr/share/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tk_data/images/logo.eps', '/usr/share/tk8.6/images/logo.eps', 'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/usr/share/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg', '/usr/share/tcl8.6/msgs/en_in.msg', 'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/usr/share/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data/msgs/mk.msg', '/usr/share/tcl8.6/msgs/mk.msg', 'DATA'),
  ('_tk_data/msgs/fr.msg', '/usr/share/tk8.6/msgs/fr.msg', 'DATA'),
  ('_tcl_data/msgs/ar_lb.msg', '/usr/share/tcl8.6/msgs/ar_lb.msg', 'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/usr/share/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/usr/share/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('_tk_data/unsupported.tcl', '/usr/share/tk8.6/unsupported.tcl', 'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/usr/share/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/usr/share/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tcl_data/msgs/nn.msg', '/usr/share/tcl8.6/msgs/nn.msg', 'DATA'),
  ('_tk_data/console.tcl', '/usr/share/tk8.6/console.tcl', 'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/usr/share/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/usr/share/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tcl_data/msgs/te.msg', '/usr/share/tcl8.6/msgs/te.msg', 'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/usr/share/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tk_data/msgs/de.msg', '/usr/share/tk8.6/msgs/de.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/usr/share/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/usr/share/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tk_data/ttk/utils.tcl', '/usr/share/tk8.6/ttk/utils.tcl', 'DATA'),
  ('_tcl_data/msgs/mr.msg', '/usr/share/tcl8.6/msgs/mr.msg', 'DATA'),
  ('_tcl_data/msgs/de_at.msg', '/usr/share/tcl8.6/msgs/de_at.msg', 'DATA'),
  ('_tcl_data/msgs/ar_sy.msg', '/usr/share/tcl8.6/msgs/ar_sy.msg', 'DATA'),
  ('_tcl_data/msgs/en_ie.msg', '/usr/share/tcl8.6/msgs/en_ie.msg', 'DATA'),
  ('_tcl_data/msgs/pt_br.msg', '/usr/share/tcl8.6/msgs/pt_br.msg', 'DATA'),
  ('_tcl_data/msgs/en_sg.msg', '/usr/share/tcl8.6/msgs/en_sg.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/usr/share/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg', '/usr/share/tcl8.6/msgs/it_ch.msg', 'DATA'),
  ('_tcl_data/msgs/es_bo.msg', '/usr/share/tcl8.6/msgs/es_bo.msg', 'DATA'),
  ('_tcl_data/msgs/mt.msg', '/usr/share/tcl8.6/msgs/mt.msg', 'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/usr/share/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/usr/share/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/usr/share/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm', '/usr/share/tcl8/8.5/msgcat-1.6.1.tm', 'DATA'),
  ('_tk_data/ttk/fonts.tcl', '/usr/share/tk8.6/ttk/fonts.tcl', 'DATA'),
  ('_tcl_data/msgs/af_za.msg', '/usr/share/tcl8.6/msgs/af_za.msg', 'DATA'),
  ('_tk_data/tclIndex', '/usr/share/tk8.6/tclIndex', 'DATA'),
  ('_tcl_data/msgs/es_pr.msg', '/usr/share/tcl8.6/msgs/es_pr.msg', 'DATA'),
  ('_tcl_data/msgs/kok_in.msg', '/usr/share/tcl8.6/msgs/kok_in.msg', 'DATA'),
  ('_tcl_data/msgs/eu.msg', '/usr/share/tcl8.6/msgs/eu.msg', 'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/usr/share/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/usr/share/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('_tk_data/tkAppInit.c', '/usr/share/tk8.6/tkAppInit.c', 'DATA'),
  ('_tcl_data/msgs/hu.msg', '/usr/share/tcl8.6/msgs/hu.msg', 'DATA'),
  ('_tcl_data/msgs/es_uy.msg', '/usr/share/tcl8.6/msgs/es_uy.msg', 'DATA'),
  ('_tcl_data/msgs/es_cr.msg', '/usr/share/tcl8.6/msgs/es_cr.msg', 'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/usr/share/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg', '/usr/share/tcl8.6/msgs/eu_es.msg', 'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/usr/share/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg', '/usr/share/tcl8.6/msgs/en_be.msg', 'DATA'),
  ('_tk_data/msgs/nl.msg', '/usr/share/tk8.6/msgs/nl.msg', 'DATA'),
  ('_tk_data/bgerror.tcl', '/usr/share/tk8.6/bgerror.tcl', 'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/usr/share/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/usr/share/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl', '/usr/share/tk8.6/ttk/aquaTheme.tcl', 'DATA'),
  ('_tcl_data/msgs/kw_gb.msg', '/usr/share/tcl8.6/msgs/kw_gb.msg', 'DATA'),
  ('_tcl_data/msgs/hi_in.msg', '/usr/share/tcl8.6/msgs/hi_in.msg', 'DATA'),
  ('_tcl_data/msgs/ro.msg', '/usr/share/tcl8.6/msgs/ro.msg', 'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/usr/share/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/usr/share/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tk_data/focus.tcl', '/usr/share/tk8.6/focus.tcl', 'DATA'),
  ('_tcl_data/msgs/sh.msg', '/usr/share/tcl8.6/msgs/sh.msg', 'DATA'),
  ('_tcl_data/msgs/es_pe.msg', '/usr/share/tcl8.6/msgs/es_pe.msg', 'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/usr/share/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/usr/share/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tk_data/msgs/cs.msg', '/usr/share/tk8.6/msgs/cs.msg', 'DATA'),
  ('tcl8/8.6/http-2.9.8.tm', '/usr/share/tcl8/8.6/http-2.9.8.tm', 'DATA'),
  ('_tk_data/msgs/da.msg', '/usr/share/tk8.6/msgs/da.msg', 'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/usr/share/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg', '/usr/share/tcl8.6/msgs/es_pa.msg', 'DATA'),
  ('_tcl_data/msgs/mr_in.msg', '/usr/share/tcl8.6/msgs/mr_in.msg', 'DATA'),
  ('_tk_data/dialog.tcl', '/usr/share/tk8.6/dialog.tcl', 'DATA'),
  ('_tcl_data/msgs/kok.msg', '/usr/share/tcl8.6/msgs/kok.msg', 'DATA'),
  ('_tcl_data/msgs/ja.msg', '/usr/share/tcl8.6/msgs/ja.msg', 'DATA'),
  ('_tcl_data/msgs/es_py.msg', '/usr/share/tcl8.6/msgs/es_py.msg', 'DATA'),
  ('_tcl_data/msgs/gv.msg', '/usr/share/tcl8.6/msgs/gv.msg', 'DATA'),
  ('_tk_data/msgs/it.msg', '/usr/share/tk8.6/msgs/it.msg', 'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/usr/share/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tk_data/optMenu.tcl', '/usr/share/tk8.6/optMenu.tcl', 'DATA'),
  ('_tcl_data/msgs/es_sv.msg', '/usr/share/tcl8.6/msgs/es_sv.msg', 'DATA'),
  ('_tk_data/fontchooser.tcl', '/usr/share/tk8.6/fontchooser.tcl', 'DATA'),
  ('_tcl_data/msgs/zh_tw.msg', '/usr/share/tcl8.6/msgs/zh_tw.msg', 'DATA'),
  ('_tcl_data/msgs/bn_in.msg', '/usr/share/tcl8.6/msgs/bn_in.msg', 'DATA'),
  ('_tcl_data/msgs/es_mx.msg', '/usr/share/tcl8.6/msgs/es_mx.msg', 'DATA'),
  ('_tcl_data/msgs/fo_fo.msg', '/usr/share/tcl8.6/msgs/fo_fo.msg', 'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/usr/share/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/usr/share/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/usr/share/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/usr/share/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tk_data/images/README', '/usr/share/tk8.6/images/README', 'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/usr/share/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/usr/share/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/usr/share/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tk_data/mkpsenc.tcl', '/usr/share/tk8.6/mkpsenc.tcl', 'DATA'),
  ('_tk_data/msgs/el.msg', '/usr/share/tk8.6/msgs/el.msg', 'DATA'),
  ('_tcl_data/tclDTrace.d', '/usr/share/tcl8.6/tclDTrace.d', 'DATA'),
  ('_tcl_data/tclIndex', '/usr/share/tcl8.6/tclIndex', 'DATA'),
  ('_tk_data/msgs/es.msg', '/usr/share/tk8.6/msgs/es.msg', 'DATA'),
  ('_tk_data/listbox.tcl', '/usr/share/tk8.6/listbox.tcl', 'DATA'),
  ('base_library.zip',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1744636240,
 [('run',
   '/home/<USER>/Documents/CodeRepository/file_extension_tool/venv/lib64/python3.13/site-packages/PyInstaller/bootloader/Linux-64bit-intel/run',
   'EXECUTABLE')],
 '/lib64/libpython3.13.so.1.0')
