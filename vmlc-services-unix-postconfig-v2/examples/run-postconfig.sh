#!/bin/bash
# Example execution script for Unix Post-Configuration v2
# Enhanced with validation and error handling

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/tmp/ansible-postconfig-$(date +%Y%m%d_%H%M%S).log"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" >&2
            echo "[$timestamp] ERROR: $message" >> "$LOG_FILE"
            ;;
        WARN)
            echo -e "${YELLOW}[WARN]${NC} $message"
            echo "[$timestamp] WARN: $message" >> "$LOG_FILE"
            ;;
        INFO)
            echo -e "${GREEN}[INFO]${NC} $message"
            echo "[$timestamp] INFO: $message" >> "$LOG_FILE"
            ;;
        DEBUG)
            if [[ "${DEBUG:-false}" == "true" ]]; then
                echo -e "${BLUE}[DEBUG]${NC} $message"
                echo "[$timestamp] DEBUG: $message" >> "$LOG_FILE"
            fi
            ;;
    esac
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Unix Post-Configuration v2 Execution Script

OPTIONS:
    -f, --fqdn FQDN              Fully Qualified Domain Name (required)
    -i, --ip IP_ADDRESS          Production IP Address (required)
    -e, --environment ENV        Environment: production, staging, development (required)
    -l, --location LOCATION      Data Center: HDC1, HDC2 (required)
    -a, --pam-action ACTION      PAM action: add, delete (default: add)
    -d, --debug                  Enable debug output
    -t, --tags TAGS              Ansible tags to run (comma-separated)
    -I, --inventory INVENTORY    Inventory file (default: inventory/sample_inventory.yml)
    -v, --vault-pass FILE        Vault password file
    -h, --help                   Show this help message

EXAMPLES:
    # Basic execution
    $0 -f server01.hcloud.healthgrp.com.sg -i ************* -e production -l HDC2

    # With debug and specific tags
    $0 -f server01.hcloud.healthgrp.com.sg -i ************* -e production -l HDC2 -d -t hostname,dns

    # Staging environment with PAM deletion
    $0 -f stg-server01.hcloud.healthgrp.com.sg -i ************** -e staging -l HDC1 -a delete

EOF
}

# Validation functions
validate_fqdn() {
    local fqdn=$1
    if [[ ! $fqdn =~ ^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$ ]]; then
        log ERROR "Invalid FQDN format: $fqdn"
        return 1
    fi
    return 0
}

validate_ip() {
    local ip=$1
    if [[ ! $ip =~ ^([0-9]{1,3}\.){3}[0-9]{1,3}$ ]]; then
        log ERROR "Invalid IP address format: $ip"
        return 1
    fi
    
    # Check each octet
    IFS='.' read -ra ADDR <<< "$ip"
    for i in "${ADDR[@]}"; do
        if [[ $i -gt 255 ]]; then
            log ERROR "Invalid IP address: $ip (octet $i > 255)"
            return 1
        fi
    done
    return 0
}

validate_environment() {
    local env=$1
    case $env in
        production|staging|development)
            return 0
            ;;
        *)
            log ERROR "Invalid environment: $env (must be production, staging, or development)"
            return 1
            ;;
    esac
}

validate_location() {
    local location=$1
    case $location in
        HDC1|HDC2)
            return 0
            ;;
        *)
            log ERROR "Invalid location: $location (must be HDC1 or HDC2)"
            return 1
            ;;
    esac
}

validate_pam_action() {
    local action=$1
    case $action in
        add|delete)
            return 0
            ;;
        *)
            log ERROR "Invalid PAM action: $action (must be add or delete)"
            return 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log INFO "Checking prerequisites..."
    
    # Check if ansible-playbook is available
    if ! command -v ansible-playbook &> /dev/null; then
        log ERROR "ansible-playbook not found. Please install Ansible."
        exit 1
    fi
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_DIR/main.yml" ]]; then
        log ERROR "main.yml not found. Please run from the project directory."
        exit 1
    fi
    
    # Check if inventory exists
    if [[ ! -f "$INVENTORY" ]]; then
        log ERROR "Inventory file not found: $INVENTORY"
        exit 1
    fi
    
    log INFO "Prerequisites check passed"
}

# Main execution function
run_ansible() {
    local ansible_cmd="ansible-playbook"
    local ansible_args=()
    
    # Basic arguments
    ansible_args+=("-i" "$INVENTORY")
    ansible_args+=("$PROJECT_DIR/main.yml")
    
    # Extra variables
    local extra_vars="{"
    extra_vars+="\"FQDN\": \"$FQDN\","
    extra_vars+="\"PRD_IP\": \"$IP_ADDRESS\","
    extra_vars+="\"Environment\": \"$ENVIRONMENT\","
    extra_vars+="\"dc_location\": \"$LOCATION\","
    extra_vars+="\"pam_action\": \"$PAM_ACTION\","
    extra_vars+="\"debug_output\": $DEBUG_OUTPUT"
    extra_vars+="}"
    
    ansible_args+=("-e" "$extra_vars")
    
    # Optional arguments
    if [[ -n "${TAGS:-}" ]]; then
        ansible_args+=("--tags" "$TAGS")
    fi
    
    if [[ -n "${VAULT_PASS:-}" ]]; then
        ansible_args+=("--vault-password-file" "$VAULT_PASS")
    fi
    
    if [[ "$DEBUG_OUTPUT" == "true" ]]; then
        ansible_args+=("-v")
    fi
    
    log INFO "Executing Ansible playbook..."
    log DEBUG "Command: $ansible_cmd ${ansible_args[*]}"
    
    # Execute ansible-playbook
    if "$ansible_cmd" "${ansible_args[@]}"; then
        log INFO "Ansible playbook execution completed successfully"
        return 0
    else
        log ERROR "Ansible playbook execution failed"
        return 1
    fi
}

# Parse command line arguments
FQDN=""
IP_ADDRESS=""
ENVIRONMENT=""
LOCATION=""
PAM_ACTION="add"
DEBUG_OUTPUT="false"
TAGS=""
INVENTORY="$PROJECT_DIR/inventory/sample_inventory.yml"
VAULT_PASS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--fqdn)
            FQDN="$2"
            shift 2
            ;;
        -i|--ip)
            IP_ADDRESS="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -l|--location)
            LOCATION="$2"
            shift 2
            ;;
        -a|--pam-action)
            PAM_ACTION="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG_OUTPUT="true"
            DEBUG="true"
            shift
            ;;
        -t|--tags)
            TAGS="$2"
            shift 2
            ;;
        -I|--inventory)
            INVENTORY="$2"
            shift 2
            ;;
        -v|--vault-pass)
            VAULT_PASS="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log ERROR "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate required arguments
if [[ -z "$FQDN" || -z "$IP_ADDRESS" || -z "$ENVIRONMENT" || -z "$LOCATION" ]]; then
    log ERROR "Missing required arguments"
    usage
    exit 1
fi

# Validate arguments
validate_fqdn "$FQDN" || exit 1
validate_ip "$IP_ADDRESS" || exit 1
validate_environment "$ENVIRONMENT" || exit 1
validate_location "$LOCATION" || exit 1
validate_pam_action "$PAM_ACTION" || exit 1

# Log execution parameters
log INFO "Starting Unix Post-Configuration v2"
log INFO "FQDN: $FQDN"
log INFO "IP Address: $IP_ADDRESS"
log INFO "Environment: $ENVIRONMENT"
log INFO "Location: $LOCATION"
log INFO "PAM Action: $PAM_ACTION"
log INFO "Debug: $DEBUG_OUTPUT"
log INFO "Log file: $LOG_FILE"

# Check prerequisites and run
check_prerequisites
if run_ansible; then
    log INFO "Post-configuration completed successfully"
    exit 0
else
    log ERROR "Post-configuration failed"
    exit 1
fi
