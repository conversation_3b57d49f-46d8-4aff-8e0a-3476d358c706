# File Extension Tool - Usage Guide

## Author

**<PERSON>**  
CES Operational Excellence Team

## Table of Contents

1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Simple File Renamer](#simple-file-renamer)
4. [Batch File Renamer](#batch-file-renamer)
5. [Version Management](#version-management)
6. [Common Use Cases](#common-use-cases)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)
9. [FAQ](#faq)

## Introduction

The File Extension Tool is designed to help you manage file extensions for transferring code and scripts to air-gapped environments. It provides several utilities:

- **Simple File Renamer**: Rename individual files to .txt and back
- **Batch File Renamer**: Rename multiple files and folders at once
- **Version Management**: Track and manage different versions of renamed files

This guide explains how to use each component effectively.

## Installation

### Prerequisites

- Windows operating system
- No Python installation required (standalone executables)

### Installation Steps

1. **Download the tool**:
   - Download the latest release from the repository
   - Extract the ZIP file to a location of your choice

2. **Verify installation**:
   - Run one of the executable files to verify it works
   - No additional installation steps are required

## Simple File Renamer

The Simple File Renamer allows you to quickly rename individual files.

### Basic Usage

1. **Launch the application**:
   - Double-click `SimpleFileRenamer.exe`

2. **Select operation mode**:
   - Choose "Convert to .txt" to change files to text format
   - Choose "Restore original extensions" to revert files

3. **Select files**:
   - Click "Browse" to select files
   - Multiple files can be selected by holding Ctrl

4. **Execute the operation**:
   - Click "Convert" to perform the selected operation
   - A confirmation dialog will show the results

### Command Line Usage

```
SimpleFileRenamer.exe --mode [convert|restore] --files "file1.py,file2.js"
```

### Options

| Option | Description |
|--------|-------------|
| Create backup | Creates a backup of files before renaming |
| Log operations | Saves a log of all rename operations |
| Preserve timestamps | Maintains original file timestamps |

## Batch File Renamer

The Batch File Renamer allows you to rename multiple files and folders at once.

### Basic Usage

1. **Launch the application**:
   - Double-click `BatchFileRenamer.exe`

2. **Select operation mode**:
   - Choose "Convert to .txt" to change files to text format
   - Choose "Restore original extensions" to revert files

3. **Select folders**:
   - Click "Browse" to select folders
   - All files within selected folders will be processed

4. **Configure options**:
   - Select file types to include/exclude
   - Choose whether to process subfolders
   - Set backup options

5. **Execute the operation**:
   - Click "Start Batch Process" to begin
   - Progress will be displayed during operation
   - A summary report will be shown upon completion

### File Type Filtering

You can specify which file types to process:

- **Include specific extensions**: Only process files with certain extensions
- **Exclude specific extensions**: Process all files except those with certain extensions
- **Use regex patterns**: Advanced filtering using regular expressions

### Folder Structure Options

- **Process subfolders**: Include all subfolders in the operation
- **Preserve folder structure**: Maintain the original folder hierarchy
- **Create mirror structure**: Create a copy of the folder structure

## Version Management

The Version Management tool helps track different versions of renamed files.

### Creating a Version

1. **Launch the application**:
   - Double-click `SimpleVersions.exe`

2. **Create a new version**:
   - Click "New Version"
   - Select the folder containing your project
   - Enter a version name and description

3. **Save the version**:
   - Click "Save Version"
   - The version information will be stored in a database

### Restoring a Version

1. **View available versions**:
   - Click "View Versions"
   - Select a version from the list

2. **Restore the version**:
   - Click "Restore"
   - Choose a destination folder
   - Click "Confirm Restore"

### Version Comparison

1. **Select versions to compare**:
   - Choose two versions from the list
   - Click "Compare"

2. **View differences**:
   - The tool will show files that differ between versions
   - You can export the comparison report

## Common Use Cases

### Preparing Code for Air-Gapped Transfer

1. Organize your code in a clean folder structure
2. Use Batch File Renamer to convert all files to .txt
3. Create a version snapshot for reference
4. Transfer the files to the air-gapped environment
5. Use Batch File Renamer on the destination to restore original extensions

### Sharing Code via Email

1. Select the files you want to share
2. Use Simple File Renamer to convert to .txt
3. Attach the files to your email
4. Provide instructions for the recipient to restore the files

### Managing Multiple Projects

1. Create a separate folder for each project
2. Use Version Management to track changes
3. Process each project separately with Batch File Renamer

## Troubleshooting

### Common Issues

1. **File access errors**:
   - Ensure you have write permissions for the files
   - Close any applications that might be using the files
   - Run the tool as administrator if necessary

2. **Extension mapping errors**:
   - Check the extension mapping file for errors
   - Restore from backup if the mapping file is corrupted

3. **Version database issues**:
   - Verify the database file is not corrupted
   - Use the "Repair Database" option if needed

### Error Messages

| Error | Solution |
|-------|----------|
| "Access denied" | Close applications using the file or run as administrator |
| "File not found" | Verify the file path is correct |
| "Invalid extension mapping" | Check or reset the extension mapping file |
| "Database error" | Repair or recreate the version database |

## Best Practices

1. **Always create backups** before performing batch operations
2. **Use descriptive version names** to easily identify versions
3. **Test on a small set of files** before processing large projects
4. **Keep a log of operations** for troubleshooting
5. **Regularly clean up old versions** to save disk space

## FAQ

**Q: Can I use this tool on non-code files?**  
A: Yes, the tool works with any file type, but it's primarily designed for code and script files.

**Q: Will renaming files affect their content?**  
A: No, only the file extension is changed. The content remains unchanged.

**Q: Can I automate the renaming process?**  
A: Yes, you can use the command-line interface for automation.

**Q: Is there a limit to how many files I can process?**  
A: There's no hard limit, but processing thousands of files may take some time.

**Q: Can I customize the extension mapping?**  
A: Yes, you can edit the extension mapping file to add custom mappings.

**Q: Will this work with files that don't have extensions?**  
A: Yes, the tool can handle files without extensions by adding .txt.
