#!/usr/bin/env pwsh
<#
.SYNOPSIS
    File Extension Renamer Tool for Air Gap Solutions
.DESCRIPTION
    A PowerShell script with GUI to rename file extensions to .txt and back for air-gapped environments.
.NOTES
    Author: <PERSON> - CES Operational Excellence Team
#>

# Add required assemblies for GUI
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Create the main form
$form = New-Object System.Windows.Forms.Form
$form.Text = 'File Extension Renamer Tool'
$form.Size = New-Object System.Drawing.Size(700, 600)
$form.StartPosition = 'CenterScreen'
$form.FormBorderStyle = 'FixedDialog'
$form.MaximizeBox = $false

# Create a title label
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(660, 30)
$titleLabel.Text = 'File Extension Renamer Tool for Air Gap Solutions'
$titleLabel.Font = New-Object System.Drawing.Font('Arial', 14, [System.Drawing.FontStyle]::Bold)
$titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
$form.Controls.Add($titleLabel)

# Create a subtitle label
$subtitleLabel = New-Object System.Windows.Forms.Label
$subtitleLabel.Location = New-Object System.Drawing.Point(20, 50)
$subtitleLabel.Size = New-Object System.Drawing.Size(660, 20)
$subtitleLabel.Text = 'Convert file extensions to .txt for air-gapped environments'
$subtitleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
$form.Controls.Add($subtitleLabel)

# Create a group box for source selection
$sourceGroupBox = New-Object System.Windows.Forms.GroupBox
$sourceGroupBox.Location = New-Object System.Drawing.Point(20, 80)
$sourceGroupBox.Size = New-Object System.Drawing.Size(660, 100)
$sourceGroupBox.Text = 'Source Selection'
$form.Controls.Add($sourceGroupBox)

# Create radio buttons for selection type
$fileRadioButton = New-Object System.Windows.Forms.RadioButton
$fileRadioButton.Location = New-Object System.Drawing.Point(20, 30)
$fileRadioButton.Size = New-Object System.Drawing.Size(100, 20)
$fileRadioButton.Text = 'Single File'
$fileRadioButton.Checked = $true
$sourceGroupBox.Controls.Add($fileRadioButton)

$folderRadioButton = New-Object System.Windows.Forms.RadioButton
$folderRadioButton.Location = New-Object System.Drawing.Point(130, 30)
$folderRadioButton.Size = New-Object System.Drawing.Size(100, 20)
$folderRadioButton.Text = 'Folder'
$sourceGroupBox.Controls.Add($folderRadioButton)

# Create source path textbox and browse button
$sourcePathLabel = New-Object System.Windows.Forms.Label
$sourcePathLabel.Location = New-Object System.Drawing.Point(20, 60)
$sourcePathLabel.Size = New-Object System.Drawing.Size(80, 20)
$sourcePathLabel.Text = 'Source Path:'
$sourceGroupBox.Controls.Add($sourcePathLabel)

$sourcePathTextBox = New-Object System.Windows.Forms.TextBox
$sourcePathTextBox.Location = New-Object System.Drawing.Point(110, 60)
$sourcePathTextBox.Size = New-Object System.Drawing.Size(440, 20)
$sourcePathTextBox.ReadOnly = $true
$sourceGroupBox.Controls.Add($sourcePathTextBox)

$sourceBrowseButton = New-Object System.Windows.Forms.Button
$sourceBrowseButton.Location = New-Object System.Drawing.Point(560, 59)
$sourceBrowseButton.Size = New-Object System.Drawing.Size(80, 23)
$sourceBrowseButton.Text = 'Browse'
$sourceGroupBox.Controls.Add($sourceBrowseButton)

# Create a group box for operation selection
$operationGroupBox = New-Object System.Windows.Forms.GroupBox
$operationGroupBox.Location = New-Object System.Drawing.Point(20, 190)
$operationGroupBox.Size = New-Object System.Drawing.Size(660, 80)
$operationGroupBox.Text = 'Operation'
$form.Controls.Add($operationGroupBox)

# Create radio buttons for operation type
$convertRadioButton = New-Object System.Windows.Forms.RadioButton
$convertRadioButton.Location = New-Object System.Drawing.Point(20, 30)
$convertRadioButton.Size = New-Object System.Drawing.Size(200, 20)
$convertRadioButton.Text = 'Convert to .txt'
$convertRadioButton.Checked = $true
$operationGroupBox.Controls.Add($convertRadioButton)

$restoreRadioButton = New-Object System.Windows.Forms.RadioButton
$restoreRadioButton.Location = New-Object System.Drawing.Point(230, 30)
$restoreRadioButton.Size = New-Object System.Drawing.Size(200, 20)
$restoreRadioButton.Text = 'Restore original extensions'
$operationGroupBox.Controls.Add($restoreRadioButton)

# Create a group box for options
$optionsGroupBox = New-Object System.Windows.Forms.GroupBox
$optionsGroupBox.Location = New-Object System.Drawing.Point(20, 280)
$optionsGroupBox.Size = New-Object System.Drawing.Size(660, 80)
$optionsGroupBox.Text = 'Options'
$form.Controls.Add($optionsGroupBox)

# Create checkbox for recursive operation
$recursiveCheckBox = New-Object System.Windows.Forms.CheckBox
$recursiveCheckBox.Location = New-Object System.Drawing.Point(20, 30)
$recursiveCheckBox.Size = New-Object System.Drawing.Size(300, 20)
$recursiveCheckBox.Text = 'Process subfolders (recursive)'
$recursiveCheckBox.Checked = $true
$optionsGroupBox.Controls.Add($recursiveCheckBox)

# Create a group box for log output
$logGroupBox = New-Object System.Windows.Forms.GroupBox
$logGroupBox.Location = New-Object System.Drawing.Point(20, 370)
$logGroupBox.Size = New-Object System.Drawing.Size(660, 150)
$logGroupBox.Text = 'Operation Log'
$form.Controls.Add($logGroupBox)

# Create log textbox
$logTextBox = New-Object System.Windows.Forms.TextBox
$logTextBox.Location = New-Object System.Drawing.Point(10, 20)
$logTextBox.Size = New-Object System.Drawing.Size(640, 120)
$logTextBox.Multiline = $true
$logTextBox.ScrollBars = 'Vertical'
$logTextBox.ReadOnly = $true
$logGroupBox.Controls.Add($logTextBox)

# Create execute button
$executeButton = New-Object System.Windows.Forms.Button
$executeButton.Location = New-Object System.Drawing.Point(250, 530)
$executeButton.Size = New-Object System.Drawing.Size(200, 30)
$executeButton.Text = 'Execute'
$executeButton.Font = New-Object System.Drawing.Font('Arial', 10, [System.Drawing.FontStyle]::Bold)
$form.Controls.Add($executeButton)

# Browse button click event
$sourceBrowseButton.Add_Click({
    if ($fileRadioButton.Checked) {
        $openFileDialog = New-Object System.Windows.Forms.OpenFileDialog
        $openFileDialog.Title = "Select a file"
        $openFileDialog.Filter = "All files (*.*)|*.*"
        if ($openFileDialog.ShowDialog() -eq 'OK') {
            $sourcePathTextBox.Text = $openFileDialog.FileName
            $logTextBox.AppendText("Selected file: $($openFileDialog.FileName)`r`n")
        }
    } else {
        $folderBrowserDialog = New-Object System.Windows.Forms.FolderBrowserDialog
        $folderBrowserDialog.Description = "Select a folder"
        if ($folderBrowserDialog.ShowDialog() -eq 'OK') {
            $sourcePathTextBox.Text = $folderBrowserDialog.SelectedPath
            $logTextBox.AppendText("Selected folder: $($folderBrowserDialog.SelectedPath)`r`n")
        }
    }
})

# Execute button click event
$executeButton.Add_Click({
    $sourcePath = $sourcePathTextBox.Text
    if (-not $sourcePath) {
        [System.Windows.Forms.MessageBox]::Show("Please select a source file or folder.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
        return
    }
    
    $isFile = $fileRadioButton.Checked
    $isConvert = $convertRadioButton.Checked
    $isRecursive = $recursiveCheckBox.Checked
    
    $logTextBox.Clear()
    $logTextBox.AppendText("Starting operation...`r`n")
    
    # Create a timestamp for the log file name
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $logDir = Join-Path $scriptDir "logs"
    
    # Create logs directory if it doesn't exist
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    $logFile = Join-Path $logDir "extension_tool_${timestamp}.log"
    
    # Function to log messages
    function Log-Message {
        param([string]$message)
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $logMessage = "[$timestamp] $message"
        $logTextBox.AppendText("$logMessage`r`n")
        Add-Content -Path $logFile -Value $logMessage
        # Force UI update
        [System.Windows.Forms.Application]::DoEvents()
    }
    
    Log-Message "Operation started"
    
    # Function to convert file extensions to .txt
    function Convert-ToTxt {
        param(
            [string]$path,
            [bool]$isFile,
            [bool]$isRecursive
        )
        
        # Create or load extension map
        $mapFile = Join-Path (Split-Path $path -Parent) "extension_map.json"
        if (Test-Path $mapFile) {
            $extensionMap = Get-Content $mapFile -Raw | ConvertFrom-Json
            $extensionMapObj = @{}
            # Convert PSCustomObject to hashtable
            foreach ($prop in $extensionMap.PSObject.Properties) {
                $extensionMapObj[$prop.Name] = $prop.Value
            }
            $extensionMap = $extensionMapObj
            Log-Message "Loaded existing extension map from $mapFile"
        } else {
            $extensionMap = @{}
            Log-Message "Created new extension map"
        }
        
        # Process a single file
        function Process-File {
            param([string]$filePath)
            
            # Skip if already .txt or if it's the map file
            if ($filePath -like "*.txt" -or $filePath -eq $mapFile) {
                Log-Message "Skipping $filePath (already .txt or map file)"
                return
            }
            
            try {
                $fileInfo = New-Object System.IO.FileInfo($filePath)
                $originalExt = $fileInfo.Extension
                $newPath = [System.IO.Path]::ChangeExtension($filePath, ".txt")
                
                # Store the original extension in the map
                $relativePath = $filePath.Replace("$([System.IO.Path]::GetDirectoryName($mapFile))\", "").Replace("$([System.IO.Path]::GetDirectoryName($mapFile))/", "")
                $extensionMap[$relativePath] = $originalExt
                
                # Rename the file
                Rename-Item -Path $filePath -NewName $newPath -Force
                Log-Message "Converted: $filePath -> $newPath"
            } catch {
                Log-Message "Error processing $filePath`: $_"
            }
        }
        
        # Process files based on input type
        if ($isFile) {
            Process-File -filePath $path
        } else {
            # Process folder
            if ($isRecursive) {
                $files = Get-ChildItem -Path $path -File -Recurse | Select-Object -ExpandProperty FullName
            } else {
                $files = Get-ChildItem -Path $path -File | Select-Object -ExpandProperty FullName
            }
            
            Log-Message "Found $($files.Count) files to process"
            
            $processedCount = 0
            foreach ($file in $files) {
                Process-File -filePath $file
                $processedCount++
                if ($processedCount % 10 -eq 0) {
                    Log-Message "Processed $processedCount of $($files.Count) files"
                }
            }
        }
        
        # Save the extension map
        $extensionMap | ConvertTo-Json | Set-Content -Path $mapFile
        Log-Message "Saved extension map to $mapFile"
        Log-Message "Conversion completed successfully"
    }
    
    # Function to restore original extensions
    function Restore-Extensions {
        param(
            [string]$path,
            [bool]$isFile
        )
        
        # Find the map file
        $mapFile = if ($isFile) {
            Join-Path (Split-Path $path -Parent) "extension_map.json"
        } else {
            Join-Path $path "extension_map.json"
        }
        
        if (-not (Test-Path $mapFile)) {
            Log-Message "Error: Extension map file not found at $mapFile"
            [System.Windows.Forms.MessageBox]::Show("Extension map file not found. Cannot restore original extensions.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            return
        }
        
        # Load the extension map
        $extensionMap = Get-Content $mapFile -Raw | ConvertFrom-Json
        Log-Message "Loaded extension map from $mapFile"
        
        $baseDir = Split-Path $mapFile -Parent
        $restoredCount = 0
        $totalFiles = ($extensionMap.PSObject.Properties | Measure-Object).Count
        
        Log-Message "Found $totalFiles files to restore"
        
        # Process each file in the map
        foreach ($item in $extensionMap.PSObject.Properties) {
            $relativePath = $item.Name
            $originalExt = $item.Value
            
            # Get the current .txt file path
            $txtFilePath = Join-Path $baseDir $relativePath
            $txtFilePath = [System.IO.Path]::ChangeExtension($txtFilePath, ".txt")
            
            # Skip if we're processing a single file and it's not the one we want
            if ($isFile -and $txtFilePath -ne [System.IO.Path]::ChangeExtension($path, ".txt")) {
                continue
            }
            
            if (Test-Path $txtFilePath) {
                try {
                    # Get the original file path with its extension
                    $originalFilePath = [System.IO.Path]::ChangeExtension($txtFilePath, $originalExt)
                    
                    # Rename the file back to its original extension
                    Rename-Item -Path $txtFilePath -NewName $originalFilePath -Force
                    Log-Message "Restored: $txtFilePath -> $originalFilePath"
                    $restoredCount++
                } catch {
                    Log-Message "Error restoring $txtFilePath`: $_"
                }
            } else {
                Log-Message "Warning: File not found for restoration: $txtFilePath"
            }
        }
        
        Log-Message "Restored $restoredCount of $totalFiles files"
        
        # If we're processing a folder and all files were restored, delete the map file
        if (-not $isFile -and $restoredCount -eq $totalFiles) {
            Remove-Item -Path $mapFile -Force
            Log-Message "Removed extension map file"
        }
        
        Log-Message "Restoration completed successfully"
    }
    
    # Execute the selected operation
    try {
        if ($isConvert) {
            Log-Message "Starting conversion to .txt..."
            Convert-ToTxt -path $sourcePath -isFile $isFile -isRecursive $isRecursive
        } else {
            Log-Message "Starting restoration of original extensions..."
            Restore-Extensions -path $sourcePath -isFile $isFile
        }
        
        Log-Message "Operation completed successfully"
        Log-Message "Log file saved to: $logFile"
        [System.Windows.Forms.MessageBox]::Show("Operation completed successfully. Log file saved to: $logFile", "Success", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
    } catch {
        Log-Message "Error: $_"
        [System.Windows.Forms.MessageBox]::Show("An error occurred: $_", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
    }
})

# Show the form
$form.Add_Shown({$form.Activate()})
[void]$form.ShowDialog()
