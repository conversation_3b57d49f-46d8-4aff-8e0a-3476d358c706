# Upgrade Guide: v1 to v2

This document outlines the key improvements and changes when upgrading from `vmlc-services-unix-postconfig-v1` to `vmlc-services-unix-postconfig-v2`.

## Summary of Improvements

### 🎯 **Critical Improvements Implemented**

| Category | v1 Issues | v2 Solutions |
|----------|-----------|--------------|
| **Ansible Best Practices** | Short module names, no FQCN | ✅ All modules use FQCN (`ansible.builtin.*`) |
| **Variable Naming** | `Environment` conflicts with reserved names | ✅ Uses `var_environment` with proper prefix |
| **Error Handling** | Excessive `ignore_errors: yes` | ✅ Structured error handling with validation |
| **Code Structure** | Monolithic playbook | ✅ Role-based modular architecture |
| **Security** | Hardcoded credentials in scripts | ✅ Externalized configuration variables |
| **Logging** | Minimal logging | ✅ Comprehensive structured logging |
| **Host Management** | Static inventory only | ✅ Dynamic host addition with `add_host` module |

## Detailed Comparison

### 1. Ansible Best Practices & AAP Compatibility

#### v1 Problems:
```yaml
# v1 - Non-compliant module usage
- name: Reboot the server
  reboot:
    msg: "Reboot initiated by Ansible"

- name: Debug output
  debug:
    var: host.stdout_lines
```

#### v2 Solutions:
```yaml
# v2 - FQCN compliant
- name: Reboot the server
  ansible.builtin.reboot:
    msg: "Reboot initiated by Ansible"

- name: Debug output
  ansible.builtin.debug:
    var: hostname_result.stdout_lines
```

### 2. Variable Naming Conventions

#### v1 Problems:
```yaml
# v1 - Conflicts with Ansible reserved names
vars:
  Environment: "{{ Environment }}"  # Conflicts with reserved name
```

#### v2 Solutions:
```yaml
# v2 - Proper variable naming
vars:
  var_environment: "{{ Environment | default('production') }}"
  var_fqdn: "{{ FQDN | default('') }}"
  var_dc_location: "{{ dc_location | default('HDC2') }}"
```

### 3. Error Handling

#### v1 Problems:
```yaml
# v1 - Poor error handling
- name: Configuring hostname
  script: ./files/set-host.py {{ FQDN }} {{ PRD_IP }}
  register: host
  ignore_errors: yes  # Masks all errors
```

#### v2 Solutions:
```yaml
# v2 - Structured error handling
- name: Configure hostname and /etc/hosts
  ansible.builtin.script: "{{ role_path }}/files/set-host.py {{ service_fqdn }} {{ service_ip }}"
  register: hostname_result
  failed_when: false

- name: Handle hostname configuration failure
  ansible.builtin.fail:
    msg: "Hostname configuration failed: {{ hostname_result.stderr | default('Unknown error') }}"
  when:
    - hostname_result.rc != 0
    - not (ignore_configuration_errors | default(false))
```

### 4. Code Structure

#### v1 Problems:
- Single monolithic playbook file
- Repeated debug tasks
- No modular organization
- Hardcoded values in Python scripts

#### v2 Solutions:
```
# v2 - Role-based structure
vmlc-services-unix-postconfig-v2/
├── main.yml                    # Clean entry point
├── roles/
│   └── unix-postconfig/
│       ├── tasks/
│       │   ├── main.yml        # Orchestration
│       │   ├── configure-hostname.yml
│       │   ├── configure-pam.yml
│       │   └── ...
│       ├── handlers/main.yml   # Event handlers
│       ├── vars/main.yml       # Centralized config
│       └── files/              # Enhanced scripts
```

### 5. Security Improvements

#### v1 Problems:
```python
# v1 - Hardcoded values in scripts
required_servers = [
    "SERVER = HISBKPVPMAS30",
    "SERVER = HISBKPPPMAS16",
    # ... hardcoded list
]

# v1 - Unsafe sudo usage
subprocess.run(['sudo', 'hostnamectl', 'set-hostname', new_hostname])
```

#### v2 Solutions:
```yaml
# v2 - Externalized configuration
netbackup_servers:
  domain_2c:
    - "**************  HISBKPVPMAS30"
    - "**************  HISBKPPPMAS16"
```

```python
# v2 - Proper privilege handling (Ansible manages sudo)
subprocess.run(['hostnamectl', 'set-hostname', new_hostname])
```

### 6. Enhanced Python Scripts

#### v1 Problems:
- No input validation
- Poor error handling
- No logging
- Hardcoded configuration

#### v2 Solutions:
```python
# v2 - Enhanced validation and logging
def validate_hostname(hostname):
    """Validate hostname format according to RFC standards"""
    if not hostname or len(hostname) > 253:
        return False
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return re.match(pattern, hostname) is not None

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")
```

## Migration Steps

### 1. Update Playbook Execution

#### v1 Command:
```bash
ansible-playbook -i hosts manage-postconfig.yml -e \
'{"FQDN": "server.domain.com", "PRD_IP": "*************", "Environment": "PRODuction", "dc_location": "HDC2"}'
```

#### v2 Command:
```bash
ansible-playbook -i inventory/sample_inventory.yml main.yml -e \
'{"FQDN": "server.domain.com", "PRD_IP": "*************", "Environment": "production", "dc_location": "HDC2"}'
```

### 2. Update Variable Names

| v1 Variable | v2 Variable | Notes |
|-------------|-------------|-------|
| `Environment` | `var_environment` | Avoids reserved name conflict |
| `debug_output` | `var_debug_output` | Consistent naming |
| Direct usage | Role variables | Passed through role interface |

### 3. Configuration Migration

1. **Extract hardcoded values** from Python scripts to `vars/main.yml`
2. **Update inventory** to use YAML format with proper structure
3. **Add ansible.cfg** for consistent configuration
4. **Implement logging** by reviewing log files in `/var/log/ansible-postconfig.log`

## Testing the Migration

### 1. Validation Test
```bash
# Test with validation only
ansible-playbook -i inventory/sample_inventory.yml main.yml \
  --check \
  -e FQDN="test.hcloud.healthgrp.com.sg" \
  -e PRD_IP="*************" \
  -e Environment="staging" \
  -e dc_location="HDC2"
```

### 2. Debug Mode Test
```bash
# Test with debug output
./examples/run-postconfig.sh \
  -f test.hcloud.healthgrp.com.sg \
  -i ************* \
  -e staging \
  -l HDC2 \
  -d
```

### 3. Specific Component Test
```bash
# Test only hostname configuration
ansible-playbook -i inventory/sample_inventory.yml main.yml \
  --tags hostname \
  -e FQDN="test.hcloud.healthgrp.com.sg" \
  -e PRD_IP="*************" \
  -e Environment="staging" \
  -e dc_location="HDC2"
```

## Benefits of v2

### Operational Benefits
- ✅ **AAP Compatibility**: Fully compatible with Ansible Automation Platform
- ✅ **Better Error Handling**: Graceful failure handling with detailed logging
- ✅ **Enhanced Security**: No hardcoded credentials, proper validation
- ✅ **Improved Maintainability**: Modular structure, easier to modify
- ✅ **Better Debugging**: Comprehensive logging and debug capabilities

### Technical Benefits
- ✅ **Idempotency**: Better checks for configuration state
- ✅ **Performance**: Optimized execution with proper handlers
- ✅ **Scalability**: Role-based structure supports easy extension
- ✅ **Standards Compliance**: Follows Ansible best practices

## Rollback Plan

If issues occur with v2, you can rollback to v1:

1. **Backup v2 configuration**:
   ```bash
   cp -r vmlc-services-unix-postconfig-v2 vmlc-services-unix-postconfig-v2.backup
   ```

2. **Use v1 temporarily**:
   ```bash
   ansible-playbook -i hosts vmlc-services-unix-postconfig-v1/manage-postconfig.yml -e '...'
   ```

3. **Restore from backups**: All v2 scripts create automatic backups of configuration files

## Support and Troubleshooting

### Common Migration Issues

1. **Variable Name Conflicts**: Update all variable references to use `var_` prefix
2. **Inventory Format**: Convert INI inventory to YAML format
3. **Module Names**: Ensure all custom modules use FQCN
4. **Error Handling**: Review and update any custom error handling logic

### Getting Help

- Review the comprehensive logging in `/var/log/ansible-postconfig.log`
- Use debug mode: `-e debug_output=true`
- Check the troubleshooting section in `README.md`
- Contact CES Operational Excellence Team for support

## Conclusion

Version 2 represents a significant improvement in code quality, security, maintainability, and operational reliability. The migration effort is justified by the substantial benefits in AAP compatibility, error handling, and long-term maintainability.
