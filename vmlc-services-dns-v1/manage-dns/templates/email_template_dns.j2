{#
   Enhanced Email Template for DNS Management Notifications with Intelligent PTR Management
   This template provides comprehensive reporting for DNS operations including:
   - A record status and details
   - Intelligent PTR zone detection results
   - PTR management operation status
   - Enhanced error reporting and troubleshooting information
   - Performance metrics and timing information

   Author: CES Operational Excellence Team
   Contributor: <PERSON> (7409)
   Version: 2.0 - Enhanced PTR Management
#}

Dear {{ team_name }},<br><br>

Please be informed that the following VM Lifecycle Non-Windows DNS A record is triggered to <b><u>{{ dns_records.action }}</u></b> via <b>{{ var_ticket }}</b>.<br><br>

<b><u>DNS A Record Status</u></b><br>
- <b>A Record Status:</b> {{ dns_records.status }}<br>
- <b>Domain:</b> {{ dns_records.domain }}<br>
- <b>DNS Server:</b> {{ dns_records.dns_server }}<br>
- <b>Hostname:</b> {{ dns_records.hostname }}<br>
- <b>IP Address:</b> {{ dns_records.ip_address if dns_records.ip_address else 'Nil' }}<br>
- <b>TTL:</b> {{ dns_records.ttl if dns_records.ttl else 'Nil' }}<br><br>

<b><u>DNS PTR Record Status (Enhanced)</u></b><br>
- <b>PTR Management Enabled:</b>
  {% if dns_records.ptr_management_enabled is defined %}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      <span style="color: green;"><b>✅ YES</b></span> - Intelligent PTR zone detection active
    {% else %}
      <span style="color: orange;"><b>⚠️ NO</b></span> - Legacy mode (A records only)
    {% endif %}
  {% else %}
    <span style="color: green;"><b>✅ YES</b></span> - Default enabled
  {% endif %}<br>
- <b>PTR DNS Server:</b> {{ dns_records.ptr_dns_server if dns_records.ptr_dns_server else 'Same as A record DNS server' }}<br>
- <b>PTR Zone Detection Result:</b>
  {% if dns_records.ptr_zone_detected and dns_records.ptr_zone_detected != 'Nil' and dns_records.ptr_zone_detected != 'None' %}
    <span style="color: green;"><b>{{ dns_records.ptr_zone_detected }}</b></span>
    {% if 'in-addr.arpa' in dns_records.ptr_zone_detected %}
      {% if dns_records.ptr_zone_detected.split('.') | length == 4 %}
        (3-octet zone - Most specific)
      {% elif dns_records.ptr_zone_detected.split('.') | length == 3 %}
        (2-octet zone - Medium specificity)
      {% elif dns_records.ptr_zone_detected.split('.') | length == 2 %}
        (1-octet zone - Least specific)
      {% endif %}
    {% endif %}
  {% else %}
    <span style="color: red;"><b>❌ No suitable PTR zones found</b></span>
  {% endif %}<br>
- <b>PTR Operation Status:</b>
  {% if dns_records.ptr_operation_status and dns_records.ptr_operation_status != 'Nil' %}
    {% if 'successfully' in dns_records.ptr_operation_status or 'created' in dns_records.ptr_operation_status or 'removed' in dns_records.ptr_operation_status %}
      <span style="color: green;"><b>✅ {{ dns_records.ptr_operation_status }}</b></span>
    {% elif 'failed' in dns_records.ptr_operation_status or 'error' in dns_records.ptr_operation_status %}
      <span style="color: red;"><b>❌ {{ dns_records.ptr_operation_status }}</b></span>
    {% elif 'disabled' in dns_records.ptr_operation_status or 'skipped' in dns_records.ptr_operation_status %}
      <span style="color: orange;"><b>⚠️ {{ dns_records.ptr_operation_status }}</b></span>
    {% else %}
      <b>{{ dns_records.ptr_operation_status }}</b>
    {% endif %}
  {% else %}
    <span style="color: gray;"><b>ℹ️ No PTR operation performed</b></span>
  {% endif %}<br>
- <b>PTR Record Value:</b> {{ dns_records.ptr_record if dns_records.ptr_record and dns_records.ptr_record != 'Nil' else 'No PTR record' }}<br><br>

<b><u>Operation Summary</u></b><br>
{% set operation_success = true %}
{% if dns_records.errors and dns_records.errors != '' %}
  {% set operation_success = false %}
{% endif %}

{% if operation_success %}
  <span style="color: green; font-size: 16px;"><b>✅ OPERATION COMPLETED SUCCESSFULLY</b></span><br>
  {% if dns_records.action == 'verify' %}
    - DNS record verification completed
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      with intelligent PTR zone detection
    {% endif %}
  {% elif dns_records.action == 'add' %}
    - DNS A record {{ 'created' if 'Added' in dns_records.status else 'verified' }}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      {% if dns_records.ptr_zone_detected and dns_records.ptr_zone_detected != 'Nil' and dns_records.ptr_zone_detected != 'None' %}
        - PTR record managed in optimal zone: {{ dns_records.ptr_zone_detected }}
      {% else %}
        - PTR zone detection completed (no suitable zones found)
      {% endif %}
    {% else %}
      - PTR management disabled (legacy mode)
    {% endif %}
  {% elif dns_records.action == 'remove' %}
    - DNS A record {{ 'removed' if 'Removed' in dns_records.status else 'verified for removal' }}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      - PTR records safely processed (only matching records affected)
    {% else %}
      - PTR management disabled (legacy mode)
    {% endif %}
  {% endif %}
{% else %}
  <span style="color: red; font-size: 16px;"><b>❌ OPERATION COMPLETED WITH ERRORS</b></span><br>
  - Please review the error details below and contact support if needed
{% endif %}<br><br>

<b><u>DNS Processing Errors & Warnings</u></b><br>
{% if dns_records.errors and dns_records.errors != '' %}
  <div style="background-color: #ffebee; padding: 10px; border-left: 4px solid #f44336;">
    <b style="color: #d32f2f;">⚠️ Error Details:</b><br>
    <pre style="color: #d32f2f; font-family: monospace;">{{ dns_records.errors }}</pre>
  </div>
{% else %}
  <span style="color: green;"><b>✅ No Processing Errors</b></span>
{% endif %}<br><br>

{% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
<b><u>PTR Management Information</u></b><br>
<div style="background-color: #e3f2fd; padding: 10px; border-left: 4px solid #2196f3;">
  <b style="color: #1976d2;">ℹ️ Enhanced PTR Management Active</b><br>
  This operation used intelligent PTR zone detection with the following features:<br>
  • <b>3-tier zone detection</b>: Automatically finds optimal PTR zones (3→2→1 octet)<br>
  • <b>Safe PTR removal</b>: Only removes PTR records matching the exact hostname<br>
  • <b>Timeout protection</b>: 10-second timeout prevents hanging operations<br>
  • <b>Error isolation</b>: PTR failures don't impact A record operations<br>
  • <b>Zone preservation</b>: Never deletes DNS zones, only manages individual records<br>
</div><br>
{% endif %}

{% if dns_records.errors and dns_records.errors != '' %}
<b><u>Troubleshooting Guidance</u></b><br>
<div style="background-color: #fff3e0; padding: 10px; border-left: 4px solid #ff9800;">
  <b style="color: #f57c00;">🔧 Next Steps for Error Resolution:</b><br>

  {% if 'PTR' in dns_records.errors %}
    <b>PTR-Related Issues:</b><br>
    • Check if PTR zones are accessible from ADMT servers<br>
    • Verify network connectivity to PTR DNS servers<br>
    • Consider running with <code>manage_ptr: false</code> for A record only operations<br>
    • PTR failures don't affect A record operations (error isolation active)<br><br>
  {% endif %}

  {% if 'timeout' in dns_records.errors or 'Timeout' in dns_records.errors %}
    <b>Timeout Issues:</b><br>
    • DNS server may be experiencing high load<br>
    • Network connectivity issues between ADMT and DNS servers<br>
    • Consider retrying the operation<br><br>
  {% endif %}

  {% if 'permission' in dns_records.errors or 'access' in dns_records.errors or 'denied' in dns_records.errors %}
    <b>Permission Issues:</b><br>
    • Verify service account has appropriate DNS management permissions<br>
    • Check if DNS zones allow updates from ADMT servers<br>
    • Contact DNS administrators for permission verification<br><br>
  {% endif %}

  <b>General Troubleshooting:</b><br>
  • Review the raw output below for detailed error information<br>
  • Check AAP job logs for complete execution details<br>
  • Contact CES Operational Excellence Team if issues persist<br>
  • For urgent issues, escalate through standard support channels<br>
</div><br>
{% endif %}

{% if operation_success %}
<b><u>Verification Steps</u></b><br>
<div style="background-color: #e8f5e8; padding: 10px; border-left: 4px solid #4caf50;">
  <b style="color: #2e7d32;">✅ Recommended Verification:</b><br>

  {% if dns_records.action == 'add' %}
    • Test DNS resolution: <code>nslookup {{ dns_records.hostname }}.{{ dns_records.domain }}</code><br>
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      • Test reverse lookup: <code>nslookup {{ dns_records.ip_address }}</code><br>
    {% endif %}
    • Verify from different network locations<br>
    • Allow 5-15 minutes for DNS propagation<br>
  {% elif dns_records.action == 'remove' %}
    • Confirm removal: <code>nslookup {{ dns_records.hostname }}.{{ dns_records.domain }}</code> (should fail)<br>
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      • Verify PTR cleanup: <code>nslookup {{ dns_records.ip_address }}</code><br>
    {% endif %}
    • Check from multiple network locations<br>
  {% elif dns_records.action == 'verify' %}
    • Current DNS state has been documented above<br>
    • Use this information for planning add/remove operations<br>
  {% endif %}
</div><br>
{% endif %}

<b><u>Technical Details & Raw Output</u></b><br>
<details>
  <summary><b>Click to expand raw PowerShell output</b></summary>
  <div style="background-color: #f5f5f5; padding: 10px; margin: 10px 0; border: 1px solid #ddd;">
    <pre style="font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">{{ raw_text | e }}</pre>
  </div>
</details><br>

<b><u>Job Information & Links</u></b><br>
- <b>AAP Job Link:</b> <a href="{{ aap_job_link }}" style="color: #1976d2;">{{ aap_job_link }}</a><br>
- <b>Ticket Reference:</b> {{ var_ticket }}<br>
- <b>Execution Time:</b> {{ ansible_date_time.iso8601 if ansible_date_time is defined else 'Not available' }}<br>
- <b>Executed By:</b> {{ ansible_user_id if ansible_user_id is defined else 'Automation Service' }}<br><br>

---<br><br>

<b><u>Support & Contact Information</u></b><br>
- <b>Primary Support:</b> CES Operational Excellence Team<br>
- <b>Email:</b> <EMAIL><br>
- <b>Documentation:</b> DNS Management Project (vmlc-services-dns-v1)<br>
- <b>Emergency Escalation:</b> Follow standard IT support procedures<br><br>

<b><u>Enhanced Features Information</u></b><br>
{% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
  <span style="color: #1976d2;"><b>🚀 Enhanced PTR Management v2.0</b></span><br>
  This notification was generated using the enhanced DNS management system with intelligent PTR zone detection capabilities.<br>
{% else %}
  <span style="color: #ff9800;"><b>⚙️ Legacy Mode Active</b></span><br>
  This operation used legacy mode (A records only). Consider enabling PTR management for enhanced functionality.<br>
{% endif %}<br>

Regards,<br>
<b>CES OPERATIONAL EXCELLENCE TEAM</b><br>
<i>DNS Management Automation v2.0 - Enhanced PTR Management</i><br><br>

<hr style="border: 1px solid #ddd;"><br>

<b>📋 INFORMATION CLASSIFICATION</b><br>
[ ] Unclassified, Non-Sensitive<br>
[ ] Restricted, Non-Sensitive<br>
[x] Restricted, Sensitive (Normal)<br>
[ ] Restricted, Sensitive (High)<br><br>

<b>⚠️ IMPORTANT NOTICES</b><br>
• This email contains DNS infrastructure information<br>
• Forward only to authorized personnel<br>
• Report any DNS-related issues immediately<br>
• Verify DNS changes before making dependent system modifications<br><br>

<div style="background-color: #f5f5f5; padding: 8px; border: 1px solid #ddd; font-size: 11px; color: #666;">
  <b>🤖 Automated Notification System</b><br>
  This is an auto-generated email from the DNS Management Automation system.<br>
  Please do not reply to this email. For support, contact the CES Operational Excellence Team.<br>
  Generated: {{ ansible_date_time.iso8601 if ansible_date_time is defined else 'Unknown' }} |
  Job ID: {{ tower_job_id if tower_job_id is defined else 'N/A' }} |
  Version: 2.0.1
</div>

