@echo off
setlocal enabledelayedexpansion

:: Simple File Extension Renamer Tool
:: Author: <PERSON> - CES Operational Excellence Team
:: Description: A simple batch script to rename file extensions to .txt and back

title Simple File Extension Renamer

:: Use user's temp directory for logs
set "log_dir=%TEMP%\ExtensionRenamer_logs"

:: Ensure temp directory exists
if not exist "%TEMP%" (
    echo Error: Temp directory not found. Using current directory instead.
    set "log_dir=ExtensionRenamer_logs"
)

:: Create logs directory
if not exist "%log_dir%" (
    mkdir "%log_dir%" 2>nul
    if errorlevel 1 (
        echo Warning: Could not create log directory. Logs will be saved to the current directory.
        set "log_dir=."
    )
)

:: Create a timestamp for the log file
set "timestamp=%date:~10,4%%date:~4,2%%date:~7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "timestamp=%timestamp: =0%"
set "logfile=%log_dir%\extension_tool_%timestamp%.log"

:: Log function
:log
echo [%date% %time%] %~1 >> "%logfile%" 2>nul
echo %~1
goto :EOF

:: Check system requirements
call :check_requirements

:: Main menu
:menu
cls
echo ===================================================
echo           SIMPLE FILE EXTENSION RENAMER
echo ===================================================
echo.
echo  This tool helps you prepare files for air-gapped
echo  environments by converting extensions to .txt
echo.
echo  1. Convert file extensions to .txt
echo  2. Restore original file extensions
echo  3. Test system compatibility
echo  4. Exit
echo.
echo ===================================================
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto convert
if "%choice%"=="2" goto restore
if "%choice%"=="3" goto test_system
if "%choice%"=="4" exit /b 0
goto menu

:: Convert file extensions to .txt
:convert
cls
echo ===================================================
echo           CONVERT EXTENSIONS TO .TXT
echo ===================================================
echo.
echo  Please select a folder to process.
echo.
echo  NOTE: This will convert all file extensions in the
echo        selected folder to .txt and create a mapping
echo        file for later restoration.
echo.
echo ===================================================
echo.
echo Press any key to select a folder...
pause > nul

:: Use folder browser dialog
set "folder="
set "vbs_script=%temp%\folder_browser.vbs"

:: Check if temp directory exists
if not exist "%temp%" (
    echo Error: Temp directory not found. Using current directory for temporary files.
    set "vbs_script=folder_browser.vbs"
)

:: Create the VBS script for folder selection
echo Option Explicit > "%vbs_script%"
echo Dim objShell, objFolder, startFolder >> "%vbs_script%"
echo Set objShell = CreateObject("Shell.Application") >> "%vbs_script%"
echo startFolder = "" >> "%vbs_script%"
echo ' Try to use My Documents as the starting folder >> "%vbs_script%"
echo On Error Resume Next >> "%vbs_script%"
echo startFolder = objShell.NameSpace(0).Self.Path >> "%vbs_script%"
echo On Error Goto 0 >> "%vbs_script%"
echo Set objFolder = objShell.BrowseForFolder(0, "Select a folder:", 0, startFolder) >> "%vbs_script%"
echo If Not objFolder Is Nothing Then >> "%vbs_script%"
echo     WScript.Echo objFolder.Self.Path >> "%vbs_script%"
echo End If >> "%vbs_script%"

:: Run the VBS script and get the selected folder
for /f "delims=" %%a in ('cscript //nologo "%vbs_script%" 2^>nul') do set "folder=%%a"

:: Clean up
if exist "%vbs_script%" del "%vbs_script%" 2>nul

:: Check if a folder was selected
if not defined folder (
    call :log "No folder selected. Returning to menu."
    goto menu
)

:: Ask if user wants to process subfolders
echo.
echo Do you want to process subfolders as well? (Y/N)
set /p process_subfolders="Your choice: "

:: Create or clear the extension map file
set "map_file=%TEMP%\extension_map_%timestamp%.txt"
if exist "%map_file%" del "%map_file%"

:: Create a copy of the map file in the selected folder for reference
set "folder_map_file=%folder%\extension_map.txt"
if exist "%folder_map_file%" del "%folder_map_file%"

call :log "Starting conversion in folder: %folder%"
call :log "Processing subfolders: %process_subfolders%"

:: Create a temporary file to store selected items
set "selection_file=%TEMP%\selected_items_%timestamp%.txt"
if exist "%selection_file%" del "%selection_file%"

:: Show file selection interface
call :show_selection_interface

:: Check if any items were selected
if not exist "%selection_file%" (
    call :log "No items selected. Returning to menu."
    goto menu
)

:: Process selected files
set "file_count=0"
set "processed_count=0"

:: Count selected files
for /f "tokens=*" %%a in ('type "%selection_file%"') do (
    set /a file_count+=1
)

call :log "Found %file_count% items to process"

:: Process each selected item
for /f "tokens=*" %%a in ('type "%selection_file%"') do (
    call :process_file "%%a"
)

call :log "Conversion completed. Processed %processed_count% of %file_count% files."
call :log "Extension map saved to: %map_file%"
call :log "A copy of the map is also saved to: %folder_map_file%"

echo.
echo Conversion completed successfully!
echo.
echo Map files saved to:
echo 1. %map_file% (primary)
echo 2. %folder_map_file% (backup copy)
echo.
echo NOTE: You will need one of these files to restore the original extensions.
echo.
echo Press any key to return to the main menu...
pause > nul
goto menu

:: Process a single file
:process_file
set "filepath=%~1"
set "filename=%~nx1"
set "extension=%~x1"

:: Verify the file exists
if not exist "%filepath%" (
    call :log "Error: File not found: %filepath%"
    goto :EOF
)

:: Skip if already .txt or if it's the map file
if /i "%extension%"==".txt" goto :EOF
if /i "%filepath%"=="%map_file%" goto :EOF
if /i "%filepath%"=="%folder_map_file%" goto :EOF

:: Get the file path without extension
set "filepath_noext=%filepath:~0,-1%%~x1"
set "newpath=%filepath_noext:.=%.txt"

:: Store the mapping
echo %newpath%=%extension% >> "%map_file%" 2>nul
if errorlevel 1 (
    call :log "Warning: Could not write to primary map file. Will try backup location."
)

echo %newpath%=%extension% >> "%folder_map_file%" 2>nul
if errorlevel 1 (
    call :log "Warning: Could not write to backup map file."
)

:: Check if we can write to at least one map file
if not exist "%map_file%" (
    if not exist "%folder_map_file%" (
        call :log "Error: Could not create any map files. Cannot proceed with conversion."
        goto :EOF
    )
)

:: Rename the file
ren "%filepath%" "%filename:.=%.txt" 2>nul
if %errorlevel% equ 0 (
    call :log "Converted: %filepath% -> %filename:.=%.txt"
    set /a processed_count+=1
) else (
    call :log "Error converting: %filepath% (File may be in use or you don't have permission)"
)
goto :EOF

:: Restore original file extensions
:restore
cls
echo ===================================================
echo           RESTORE ORIGINAL EXTENSIONS
echo ===================================================
echo.
echo  Please select the folder containing files to restore.
echo  This folder should contain the extension_map.txt file.
echo.
echo ===================================================
echo.
echo Press any key to select a folder...
pause > nul

:: Use folder browser dialog
set "folder="
set "vbs_script=%temp%\folder_browser.vbs"

:: Check if temp directory exists
if not exist "%temp%" (
    echo Error: Temp directory not found. Using current directory for temporary files.
    set "vbs_script=folder_browser.vbs"
)

:: Create the VBS script for folder selection
echo Option Explicit > "%vbs_script%"
echo Dim objShell, objFolder, startFolder >> "%vbs_script%"
echo Set objShell = CreateObject("Shell.Application") >> "%vbs_script%"
echo startFolder = "" >> "%vbs_script%"
echo ' Try to use My Documents as the starting folder >> "%vbs_script%"
echo On Error Resume Next >> "%vbs_script%"
echo startFolder = objShell.NameSpace(0).Self.Path >> "%vbs_script%"
echo On Error Goto 0 >> "%vbs_script%"
echo Set objFolder = objShell.BrowseForFolder(0, "Select a folder:", 0, startFolder) >> "%vbs_script%"
echo If Not objFolder Is Nothing Then >> "%vbs_script%"
echo     WScript.Echo objFolder.Self.Path >> "%vbs_script%"
echo End If >> "%vbs_script%"

:: Run the VBS script and get the selected folder
for /f "delims=" %%a in ('cscript //nologo "%vbs_script%" 2^>nul') do set "folder=%%a"

:: Clean up
if exist "%vbs_script%" del "%vbs_script%" 2>nul

:: Check if a folder was selected
if not defined folder (
    call :log "No folder selected. Returning to menu."
    goto menu
)

:: Check if the map file exists in the folder
set "folder_map_file=%folder%\extension_map.txt"

:: Ask user for timestamp if needed
if not exist "%folder_map_file%" (
    echo.
    echo The extension_map.txt file was not found in the selected folder.
    echo.
    echo Please enter the timestamp from the conversion process
    echo (format: YYYYMMDD_HHMMSS) or leave blank to search for any map file:
    echo.
    set /p "timestamp_input=Timestamp: "

    if not "%timestamp_input%"=="" (
        set "map_file=%TEMP%\extension_map_%timestamp_input%.txt"
    ) else (
        :: Try to find any extension map file in temp directory
        for /f "delims=" %%f in ('dir /b "%TEMP%\extension_map_*.txt" 2^>nul') do (
            set "map_file=%TEMP%\%%f"
            goto :found_map
        )

        echo.
        echo Error: No extension map files found.
        echo Cannot restore original extensions without a mapping file.
        echo.
        call :log "Error: No extension map files found"
        echo Press any key to return to the main menu...
        pause > nul
        goto menu

        :found_map
    )
) else (
    set "map_file=%folder_map_file%"
)

if not exist "%map_file%" (
    echo.
    echo Error: Could not find a valid extension map file.
    echo Cannot restore original extensions without the mapping file.
    echo.
    call :log "Error: Could not find a valid extension map file"
    echo Press any key to return to the main menu...
    pause > nul
    goto menu
)

call :log "Starting restoration from folder: %folder%"
call :log "Using map file: %map_file%"

:: Create a temporary file to store selected items
set "selection_file=%TEMP%\selected_items_%timestamp%.txt"
if exist "%selection_file%" del "%selection_file%"

:: Load all files from the map file
set "all_files_file=%TEMP%\all_files_%timestamp%.txt"
if exist "%all_files_file%" del "%all_files_file%"

:: Extract all file paths from the map file
for /f "tokens=1,2 delims==" %%a in ('type "%map_file%"') do (
    echo %%a >> "%all_files_file%"
)

:: Show file selection interface for restoration
call :show_restore_interface

:: Check if any items were selected
if not exist "%selection_file%" (
    call :log "No items selected for restoration. Returning to menu."
    goto menu
)

:: Process selected files
set "restored_count=0"
set "total_files=0"

:: Count total files to restore
for /f "tokens=*" %%a in ('type "%selection_file%"') do (
    set /a total_files+=1
)

call :log "Found %total_files% files to restore"

:: Process each selected file
for /f "tokens=*" %%a in ('type "%selection_file%"') do (
    set "txt_file=%%a"

    :: Find the original extension in the map file
    for /f "tokens=1,2 delims==" %%b in ('type "%map_file%"') do (
        if "%%b"=="!txt_file!" (
            set "original_ext=%%c"

            :: Check if the .txt file exists
            if exist "!txt_file!" (
                :: Get the file path without .txt extension
                set "original_file=!txt_file:.txt=!!original_ext!"

                :: Rename the file back to its original extension
                ren "!txt_file!" "!txt_file:.txt=!!original_ext!" 2>nul
                if !errorlevel! equ 0 (
                    call :log "Restored: !txt_file! -> !txt_file:.txt=!!original_ext!"
                    set /a restored_count+=1
                ) else (
                    call :log "Error restoring: !txt_file!"
                )
            ) else (
                call :log "Warning: File not found for restoration: !txt_file!"
            )
        )
    )
)

:: Clean up
del "%all_files_file%" 2>nul

call :log "Restoration completed. Restored %restored_count% of %total_files% files."

:: Delete the map file if all files were restored
if %restored_count% equ %total_files% (
    del "%map_file%" 2>nul
    call :log "Removed extension map file"
)

echo.
echo Restoration completed successfully!
echo.
echo Press any key to return to the main menu...
pause > nul
goto menu

:: File selection interface function
:show_selection_interface
setlocal enabledelayedexpansion

:: Create a temporary VBS script for the selection interface
set "vbs_script=%TEMP%\file_selector.vbs"

:: Check if temp directory exists
if not exist "%TEMP%" (
    echo Error: Temp directory not found. Using current directory for temporary files.
    set "vbs_script=file_selector.vbs"
)

echo Option Explicit > "%vbs_script%"
echo Dim fso, folder, files, subfolders, file, subfolder, selectedItems, userChoice >> "%vbs_script%"
echo Dim selectionFile >> "%vbs_script%"
echo selectionFile = "%selection_file:\=\\%" >> "%vbs_script%"

echo Set fso = CreateObject("Scripting.FileSystemObject") >> "%vbs_script%"
echo Set selectedItems = CreateObject("Scripting.Dictionary") >> "%vbs_script%"

echo ' Create the selection form >> "%vbs_script%"
echo Sub CreateSelectionForm() >> "%vbs_script%"
echo     Dim form, fileList, selectAllBtn, deselectAllBtn, okBtn, cancelBtn, statusLabel >> "%vbs_script%"
echo     Dim width, height, i, item, isRecursive >> "%vbs_script%"
echo     width = 600 >> "%vbs_script%"
echo     height = 500 >> "%vbs_script%"

echo     ' Create the form >> "%vbs_script%"
echo     Set form = CreateObject("WScript.Shell").Exec("mshta.exe ""about:<html><head><title>File Selection</title><style>body{font-family:Arial;margin:10px;}h2{margin-top:0;}#fileList{width:100%%;height:300px;border:1px solid #ccc;overflow:auto;}#buttons{margin-top:10px;}button{padding:5px 10px;margin-right:10px;}</style><script>function selectAll(){var items=document.getElementsByTagName('input');for(var i=0;i<items.length;i++){if(items[i].type=='checkbox')items[i].checked=true;}}function deselectAll(){var items=document.getElementsByTagName('input');for(var i=0;i<items.length;i++){if(items[i].type=='checkbox')items[i].checked=false;}}function getSelected(){var items=document.getElementsByTagName('input');var selected=[];for(var i=0;i<items.length;i++){if(items[i].type=='checkbox' && items[i].checked)selected.push(items[i].value);}return selected.join('|');}function selectChildren(cb,prefix){var items=document.getElementsByTagName('input');for(var i=0;i<items.length;i++){if(items[i].type=='checkbox' && items[i].value.indexOf(prefix+'\\')===0){items[i].checked=cb.checked;}}}</script></head><body><h2>Select Files and Folders</h2><div id='fileList'></div><div id='buttons'><button onclick='selectAll()'>Select All</button><button onclick='deselectAll()'>Deselect All</button><button onclick='window.returnValue=getSelected();window.close();'>OK</button><button onclick='window.close();'>Cancel</button></div><div id='status'></div></body></html>"") >> "%vbs_script%"

echo     ' Wait for the form to initialize >> "%vbs_script%"
echo     WScript.Sleep 500 >> "%vbs_script%"

echo     ' Get the document object >> "%vbs_script%"
echo     Set fileList = form.StdIn.ReadAll >> "%vbs_script%"

echo     ' Build the file list HTML >> "%vbs_script%"
echo     Dim html, indent >> "%vbs_script%"
echo     html = "" >> "%vbs_script%"

echo     ' Process the root folder >> "%vbs_script%"
echo     isRecursive = %process_subfolders% = "Y" >> "%vbs_script%"
echo     html = html & "<div><input type='checkbox' id='root' value='" & "%folder:\=\\%" & "' onchange='selectChildren(this,this.value)'><label for='root'><b>" & "%folder:\=\\%" & "</b></label></div>" >> "%vbs_script%"

echo     ' Add files in the root folder >> "%vbs_script%"
echo     indent = "&nbsp;&nbsp;&nbsp;&nbsp;" >> "%vbs_script%"
echo     Set folder = fso.GetFolder("%folder%") >> "%vbs_script%"
echo     Set files = folder.Files >> "%vbs_script%"

echo     For Each file in files >> "%vbs_script%"
echo         html = html & "<div>" & indent & "<input type='checkbox' id='" & Replace(file.Path, "\\", "_") & "' value='" & file.Path & "'><label for='" & Replace(file.Path, "\\", "_") & "'>" & file.Name & "</label></div>" >> "%vbs_script%"
echo     Next >> "%vbs_script%"

echo     ' Process subfolders if recursive >> "%vbs_script%"
echo     If isRecursive Then >> "%vbs_script%"
echo         Set subfolders = folder.SubFolders >> "%vbs_script%"
echo         For Each subfolder in subfolders >> "%vbs_script%"
echo             AddFolderToHTML subfolder, html, indent >> "%vbs_script%"
echo         Next >> "%vbs_script%"
echo     End If >> "%vbs_script%"

echo     ' Update the file list in the form >> "%vbs_script%"
echo     form.StdIn.WriteLine "document.getElementById('fileList').innerHTML='" & Replace(html, "'", "\'") & "';" >> "%vbs_script%"

echo     ' Get the selected items >> "%vbs_script%"
echo     Dim result >> "%vbs_script%"
echo     result = form.StdOut.ReadAll >> "%vbs_script%"

echo     ' Process the selected items >> "%vbs_script%"
echo     If Len(result) > 0 Then >> "%vbs_script%"
echo         Dim selectedPaths >> "%vbs_script%"
echo         selectedPaths = Split(result, "|") >> "%vbs_script%"

echo         ' Create the selection file >> "%vbs_script%"
echo         Dim selFile >> "%vbs_script%"
echo         Set selFile = fso.CreateTextFile(selectionFile, True) >> "%vbs_script%"

echo         ' Write each selected path to the file >> "%vbs_script%"
echo         For i = 0 To UBound(selectedPaths) >> "%vbs_script%"
echo             If Len(selectedPaths(i)) > 0 Then >> "%vbs_script%"
echo                 ' Check if it's a folder >> "%vbs_script%"
echo                 If fso.FolderExists(selectedPaths(i)) Then >> "%vbs_script%"
echo                     ' Add all files in this folder and subfolders >> "%vbs_script%"
echo                     AddFolderContents selectedPaths(i), selFile, isRecursive >> "%vbs_script%"
echo                 Else >> "%vbs_script%"
echo                     ' Add the file >> "%vbs_script%"
echo                     selFile.WriteLine selectedPaths(i) >> "%vbs_script%"
echo                 End If >> "%vbs_script%"
echo             End If >> "%vbs_script%"
echo         Next >> "%vbs_script%"

echo         selFile.Close >> "%vbs_script%"
echo     End If >> "%vbs_script%"
echo End Sub >> "%vbs_script%"

echo ' Add a folder to the HTML >> "%vbs_script%"
echo Sub AddFolderToHTML(folder, ByRef html, indent) >> "%vbs_script%"
echo     Dim file, subfolder, newIndent >> "%vbs_script%"
echo     newIndent = indent & "&nbsp;&nbsp;&nbsp;&nbsp;" >> "%vbs_script%"

echo     ' Add the folder >> "%vbs_script%"
echo     html = html & "<div>" & indent & "<input type='checkbox' id='" & Replace(folder.Path, "\\", "_") & "' value='" & folder.Path & "' onchange='selectChildren(this,this.value)'><label for='" & Replace(folder.Path, "\\", "_") & "'><b>" & folder.Name & "</b></label></div>" >> "%vbs_script%"

echo     ' Add files in this folder >> "%vbs_script%"
echo     For Each file in folder.Files >> "%vbs_script%"
echo         html = html & "<div>" & newIndent & "<input type='checkbox' id='" & Replace(file.Path, "\\", "_") & "' value='" & file.Path & "'><label for='" & Replace(file.Path, "\\", "_") & "'>" & file.Name & "</label></div>" >> "%vbs_script%"
echo     Next >> "%vbs_script%"

echo     ' Process subfolders >> "%vbs_script%"
echo     For Each subfolder in folder.SubFolders >> "%vbs_script%"
echo         AddFolderToHTML subfolder, html, newIndent >> "%vbs_script%"
echo     Next >> "%vbs_script%"
echo End Sub >> "%vbs_script%"

echo ' Add all files in a folder to the selection file >> "%vbs_script%"
echo Sub AddFolderContents(folderPath, selFile, isRecursive) >> "%vbs_script%"
echo     Dim folder, file, subfolder >> "%vbs_script%"
echo     Set folder = fso.GetFolder(folderPath) >> "%vbs_script%"

echo     ' Add all files in this folder >> "%vbs_script%"
echo     For Each file in folder.Files >> "%vbs_script%"
echo         selFile.WriteLine file.Path >> "%vbs_script%"
echo     Next >> "%vbs_script%"

echo     ' Process subfolders if recursive >> "%vbs_script%"
echo     If isRecursive Then >> "%vbs_script%"
echo         For Each subfolder in folder.SubFolders >> "%vbs_script%"
echo             AddFolderContents subfolder.Path, selFile, True >> "%vbs_script%"
echo         Next >> "%vbs_script%"
echo     End If >> "%vbs_script%"
echo End Sub >> "%vbs_script%"

echo ' Run the selection form >> "%vbs_script%"
echo CreateSelectionForm >> "%vbs_script%"

:: Run the VBS script
cscript //nologo "%vbs_script%" 2>nul

:: Clean up
del "%vbs_script%" 2>nul

endlocal
goto :EOF

:: Restore file selection interface function
:show_restore_interface
setlocal enabledelayedexpansion

:: Create a temporary VBS script for the selection interface
set "vbs_script=%TEMP%\restore_selector.vbs"

:: Check if temp directory exists
if not exist "%TEMP%" (
    echo Error: Temp directory not found. Using current directory for temporary files.
    set "vbs_script=restore_selector.vbs"
)

echo Option Explicit > "%vbs_script%"
echo Dim fso, selectedItems, allFilesFile, selectionFile >> "%vbs_script%"
echo allFilesFile = "%all_files_file:\=\\%" >> "%vbs_script%"
echo selectionFile = "%selection_file:\=\\%" >> "%vbs_script%"

echo Set fso = CreateObject("Scripting.FileSystemObject") >> "%vbs_script%"

echo ' Create the selection form >> "%vbs_script%"
echo Sub CreateSelectionForm() >> "%vbs_script%"
echo     Dim form, fileList, selectAllBtn, deselectAllBtn, okBtn, cancelBtn, statusLabel >> "%vbs_script%"
echo     Dim width, height, i, item, fileDict, filePath, fileName, folderDict, folderPath, folderName >> "%vbs_script%"
echo     width = 600 >> "%vbs_script%"
echo     height = 500 >> "%vbs_script%"

echo     ' Create the form >> "%vbs_script%"
echo     Set form = CreateObject("WScript.Shell").Exec("mshta.exe ""about:<html><head><title>File Restoration</title><style>body{font-family:Arial;margin:10px;}h2{margin-top:0;}#fileList{width:100%%;height:300px;border:1px solid #ccc;overflow:auto;}#buttons{margin-top:10px;}button{padding:5px 10px;margin-right:10px;}</style><script>function selectAll(){var items=document.getElementsByTagName('input');for(var i=0;i<items.length;i++){if(items[i].type=='checkbox')items[i].checked=true;}}function deselectAll(){var items=document.getElementsByTagName('input');for(var i=0;i<items.length;i++){if(items[i].type=='checkbox')items[i].checked=false;}}function getSelected(){var items=document.getElementsByTagName('input');var selected=[];for(var i=0;i<items.length;i++){if(items[i].type=='checkbox' && items[i].checked)selected.push(items[i].value);}return selected.join('|');}function selectChildren(cb,prefix){var items=document.getElementsByTagName('input');for(var i=0;i<items.length;i++){if(items[i].type=='checkbox' && items[i].value.indexOf(prefix+'\\')===0){items[i].checked=cb.checked;}}}</script></head><body><h2>Select Files to Restore</h2><div id='fileList'></div><div id='buttons'><button onclick='selectAll()'>Select All</button><button onclick='deselectAll()'>Deselect All</button><button onclick='window.returnValue=getSelected();window.close();'>OK</button><button onclick='window.close();'>Cancel</button></div><div id='status'></div></body></html>"") >> "%vbs_script%"

echo     ' Wait for the form to initialize >> "%vbs_script%"
echo     WScript.Sleep 500 >> "%vbs_script%"

echo     ' Get the document object >> "%vbs_script%"
echo     Set fileList = form.StdIn.ReadAll >> "%vbs_script%"

echo     ' Build the file list HTML >> "%vbs_script%"
echo     Dim html, indent, folderHtml >> "%vbs_script%"
echo     html = "" >> "%vbs_script%"

echo     ' Create dictionaries to organize files by folder >> "%vbs_script%"
echo     Set fileDict = CreateObject("Scripting.Dictionary") >> "%vbs_script%"
echo     Set folderDict = CreateObject("Scripting.Dictionary") >> "%vbs_script%"

echo     ' Read all files from the all_files_file >> "%vbs_script%"
echo     Dim allFiles, file >> "%vbs_script%"
echo     Set allFiles = fso.OpenTextFile(allFilesFile, 1) >> "%vbs_script%"

echo     ' Process each file and organize by folder >> "%vbs_script%"
echo     Do Until allFiles.AtEndOfStream >> "%vbs_script%"
echo         filePath = allFiles.ReadLine >> "%vbs_script%"
echo         If Len(filePath) > 0 Then >> "%vbs_script%"
echo             folderPath = fso.GetParentFolderName(filePath) >> "%vbs_script%"

echo             ' Add folder to dictionary if not exists >> "%vbs_script%"
echo             If Not folderDict.Exists(folderPath) Then >> "%vbs_script%"
echo                 folderDict.Add folderPath, CreateObject("Scripting.Dictionary") >> "%vbs_script%"
echo             End If >> "%vbs_script%"

echo             ' Add file to folder's dictionary >> "%vbs_script%"
echo             fileName = fso.GetFileName(filePath) >> "%vbs_script%"
echo             folderDict(folderPath).Add filePath, fileName >> "%vbs_script%"
echo         End If >> "%vbs_script%"
echo     Loop >> "%vbs_script%"

echo     allFiles.Close >> "%vbs_script%"

echo     ' Process each folder and its files >> "%vbs_script%"
echo     For Each folderPath In folderDict.Keys >> "%vbs_script%"
echo         folderName = folderPath >> "%vbs_script%"
echo         If folderName = "" Then folderName = "Root" >> "%vbs_script%"

echo         ' Add folder to HTML >> "%vbs_script%"
echo         html = html & "<div><input type='checkbox' id='" & Replace(folderPath, "\\", "_") & "' value='" & folderPath & "' onchange='selectChildren(this,this.value)'><label for='" & Replace(folderPath, "\\", "_") & "'><b>" & folderName & "</b></label></div>" >> "%vbs_script%"

echo         ' Add files in this folder >> "%vbs_script%"
echo         indent = "&nbsp;&nbsp;&nbsp;&nbsp;" >> "%vbs_script%"
echo         For Each filePath In folderDict(folderPath).Keys >> "%vbs_script%"
echo             fileName = folderDict(folderPath)(filePath) >> "%vbs_script%"
echo             html = html & "<div>" & indent & "<input type='checkbox' id='" & Replace(filePath, "\\", "_") & "' value='" & filePath & "'><label for='" & Replace(filePath, "\\", "_") & "'>" & fileName & "</label></div>" >> "%vbs_script%"
echo         Next >> "%vbs_script%"
echo     Next >> "%vbs_script%"

echo     ' Update the file list in the form >> "%vbs_script%"
echo     form.StdIn.WriteLine "document.getElementById('fileList').innerHTML='" & Replace(html, "'", "\'") & "';" >> "%vbs_script%"

echo     ' Get the selected items >> "%vbs_script%"
echo     Dim result >> "%vbs_script%"
echo     result = form.StdOut.ReadAll >> "%vbs_script%"

echo     ' Process the selected items >> "%vbs_script%"
echo     If Len(result) > 0 Then >> "%vbs_script%"
echo         Dim selectedPaths >> "%vbs_script%"
echo         selectedPaths = Split(result, "|") >> "%vbs_script%"

echo         ' Create the selection file >> "%vbs_script%"
echo         Dim selFile >> "%vbs_script%"
echo         Set selFile = fso.CreateTextFile(selectionFile, True) >> "%vbs_script%"

echo         ' Write each selected path to the file >> "%vbs_script%"
echo         For i = 0 To UBound(selectedPaths) >> "%vbs_script%"
echo             If Len(selectedPaths(i)) > 0 Then >> "%vbs_script%"
echo                 ' Check if it's a folder >> "%vbs_script%"
echo                 If fso.FolderExists(selectedPaths(i)) Then >> "%vbs_script%"
echo                     ' Add all files in this folder >> "%vbs_script%"
echo                     For Each filePath In folderDict(selectedPaths(i)).Keys >> "%vbs_script%"
echo                         selFile.WriteLine filePath >> "%vbs_script%"
echo                     Next >> "%vbs_script%"
echo                 Else >> "%vbs_script%"
echo                     ' Add the file >> "%vbs_script%"
echo                     selFile.WriteLine selectedPaths(i) >> "%vbs_script%"
echo                 End If >> "%vbs_script%"
echo             End If >> "%vbs_script%"
echo         Next >> "%vbs_script%"

echo         selFile.Close >> "%vbs_script%"
echo     End If >> "%vbs_script%"
echo End Sub >> "%vbs_script%"

echo ' Run the selection form >> "%vbs_script%"
echo CreateSelectionForm >> "%vbs_script%"

:: Run the VBS script
cscript //nologo "%vbs_script%" 2>nul

:: Clean up
del "%vbs_script%" 2>nul

endlocal
goto :EOF

:: Check system requirements function
:check_requirements
setlocal

:: Check if we can create temporary files
set "test_file=%TEMP%\test_file_%random%.txt"
echo Test > "%test_file%" 2>nul
if not exist "%test_file%" (
    echo Warning: Cannot write to TEMP directory. Some features may not work properly.
    echo The script will try to use the current directory for temporary files.
) else (
    del "%test_file%" 2>nul
)

:: Check if cscript is available
cscript //? >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Windows Script Host (cscript) is not available.
    echo The file selection interface may not work properly.
)

:: Check if we can run VBScript
set "test_vbs=%TEMP%\test_%random%.vbs"
echo WScript.Echo "VBScript Test" > "%test_vbs%" 2>nul
cscript //nologo "%test_vbs%" >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: VBScript execution is disabled on this system.
    echo The file selection interface may not work properly.
)
if exist "%test_vbs%" del "%test_vbs%" 2>nul

endlocal
goto :EOF

:: Test system compatibility function
:test_system
cls
echo ===================================================
echo           SYSTEM COMPATIBILITY TEST
echo ===================================================
echo.
echo Testing system compatibility...
echo.

:: Test TEMP directory
echo 1. Testing TEMP directory access...
set "test_file=%TEMP%\test_file_%random%.txt"
echo Test > "%test_file%" 2>nul
if exist "%test_file%" (
    echo    SUCCESS: Can write to TEMP directory (%TEMP%)
    del "%test_file%" 2>nul
) else (
    echo    FAILED: Cannot write to TEMP directory (%TEMP%)
    echo    The script will try to use the current directory instead.
)

:: Test current directory
echo 2. Testing current directory access...
set "test_file=test_file_%random%.txt"
echo Test > "%test_file%" 2>nul
if exist "%test_file%" (
    echo    SUCCESS: Can write to current directory
    del "%test_file%" 2>nul
) else (
    echo    FAILED: Cannot write to current directory
    echo    This may cause problems with the script operation.
)

:: Test Windows Script Host
echo 3. Testing Windows Script Host (cscript)...
cscript //? >nul 2>&1
if %errorlevel% equ 0 (
    echo    SUCCESS: Windows Script Host is available
) else (
    echo    FAILED: Windows Script Host is not available
    echo    The file selection interface will not work.
)

:: Test VBScript execution
echo 4. Testing VBScript execution...
set "test_vbs=%TEMP%\test_%random%.vbs"
echo WScript.Echo "VBScript Test" > "%test_vbs%" 2>nul
set vbs_output=
for /f "delims=" %%a in ('cscript //nologo "%test_vbs%" 2^>nul') do set vbs_output=%%a
if "%vbs_output%"=="VBScript Test" (
    echo    SUCCESS: VBScript execution is working
) else (
    echo    FAILED: VBScript execution is not working
    echo    The file selection interface will not work.
)
if exist "%test_vbs%" del "%test_vbs%" 2>nul

:: Test folder browser dialog
echo 5. Testing folder browser dialog...
set "test_vbs=%TEMP%\test_folder_browser.vbs"
echo On Error Resume Next > "%test_vbs%"
echo Set objShell = CreateObject("Shell.Application") >> "%test_vbs%"
echo Set objFolder = objShell.BrowseForFolder(0, "Test (Cancel to close)", 0, 0) >> "%test_vbs%"
echo If Err.Number <> 0 Then >> "%test_vbs%"
echo     WScript.Echo "ERROR" >> "%test_vbs%"
echo Else >> "%test_vbs%"
echo     WScript.Echo "OK" >> "%test_vbs%"
echo End If >> "%test_vbs%"

set fb_result=
for /f "delims=" %%a in ('cscript //nologo "%test_vbs%" 2^>nul') do set fb_result=%%a
if "%fb_result%"=="OK" (
    echo    SUCCESS: Folder browser dialog is working
) else (
    echo    FAILED: Folder browser dialog is not working
    echo    You may not be able to select folders.
)
if exist "%test_vbs%" del "%test_vbs%" 2>nul

echo.
echo Test completed. Press any key to return to the main menu...
pause > nul
goto menu
