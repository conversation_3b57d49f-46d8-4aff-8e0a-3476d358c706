# Batch File Extension Renamer Tool

A simple batch script with GUI to rename file extensions to .txt and back for air-gapped environments.

**Author:** <PERSON> - CES Operational Excellence Team

## Features

- User-friendly GUI interface built with PowerShell
- Convert file extensions to .txt for air-gapped environments
- Restore original file extensions from .txt
- Process individual files or entire folders
- Option for recursive processing of subfolders
- Detailed operation logging
- No Python installation required

## Requirements

- Windows operating system
- Administrator privileges (for some file operations)

## How to Use

1. Double-click the `FileExtensionRenamer.bat` file
2. If prompted, allow the script to run with administrator privileges
3. In the GUI:
   - Select "Single File" or "Folder" as your source type
   - Click "Browse" to select the source file or folder
   - Choose "Convert to .txt" or "Restore original extensions" as the operation
   - Check "Process subfolders" if you want to include subfolders (for folder operations)
   - Click "Execute" to start the operation

## How It Works

### Convert to .txt Mode

1. Creates a JSON map file (`extension_map.json`) that records the original extension of each file
2. <PERSON><PERSON>s selected files to have a `.txt` extension
3. Logs all operations to a log file in the `logs` directory

### Restore Mode

1. Reads the JSON map file to identify original file extensions
2. Renames `.txt` files back to their original extensions
3. Logs all operations to a log file in the `logs` directory
4. Removes the map file after successful restoration (for folder operations)

## Notes

- The tool automatically creates a `logs` directory in the same location as the script
- Log files are named with timestamps for easy identification
- The extension map file (`extension_map.json`) is created in the same directory as the processed files
- Files that already have a `.txt` extension are skipped during conversion
- The tool will not process the extension map file itself

## Troubleshooting

If you encounter any issues:

1. Check the operation log in the GUI for error messages
2. Review the log file in the `logs` directory for detailed information
3. Ensure you have the necessary permissions to modify the files
4. If restoring extensions fails, verify that the extension map file exists and is valid

## Advanced Usage

- For large directories, the operation may take some time. The log window will show progress updates.
- The extension map file is a JSON file that can be manually edited if needed.
- You can use the tool to convert only specific files by selecting them individually.
