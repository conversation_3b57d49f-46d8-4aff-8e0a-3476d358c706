# VM Post-Configuration Playbook for RHEL/Oracle Linux - Version 2
# Enhanced with Ansible best practices, proper error handling, and role-based structure
# Configures hostname, PAM accounts, backup, time sync, and DNS resolution
# Supports dynamic host addition for newly provisioned servers
# Author: CES Operational Excellence Team
# Version: 2.0

- name: Dynamic Host Management and VM Post-Configuration
  hosts: localhost
  gather_facts: false
  connection: local

  vars:
    # Convert conflicting variable names to use var_ prefix
    var_fqdn: "{{ FQDN | default('') }}"
    var_prd_ip: "{{ PRD_IP | default('') }}"
    var_environment: "{{ Environment | default('production') }}"
    var_pam_action: "{{ pam_action | default('add') }}"
    var_dc_location: "{{ dc_location | default('HDC2') }}"
    var_debug_output: "{{ debug_output | default(false) }}"
    var_ignore_errors: "{{ ignore_errors | default(false) }}"
    var_filesystem_payload: "{{ filesystem_payload | default([]) }}"

    # Dynamic host configuration
    var_ansible_user: "{{ ansible_user | default('ansible') }}"
    var_ansible_ssh_private_key_file: "{{ ansible_ssh_private_key_file | default('~/.ssh/id_rsa') }}"
    var_ansible_python_interpreter: "{{ ansible_python_interpreter | default('/usr/bin/python3') }}"
    var_ansible_ssh_timeout: "{{ ansible_ssh_timeout | default(30) }}"
    var_ansible_command_timeout: "{{ ansible_command_timeout | default(300) }}"

    # Logging configuration
    log_file: "/var/log/ansible-postconfig.log"

  tasks:
    - name: Validate required variables for dynamic host addition
      ansible.builtin.assert:
        that:
          - var_fqdn != ""
          - var_prd_ip != ""
          - var_dc_location in ['HDC1', 'HDC2']
          - var_environment in ['production', 'staging', 'development']
        fail_msg: "Required variables missing or invalid. Check FQDN, PRD_IP, dc_location, and Environment."
        success_msg: "All required variables validated successfully."

    - name: Initialize logging for dynamic host management
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Starting dynamic host management for {{ var_fqdn }}"
        create: true
        mode: '0644'

    - name: Check if host already exists in inventory
      ansible.builtin.set_fact:
        host_exists_in_inventory: "{{ var_fqdn in groups['all'] | default([]) }}"

    - name: Log host inventory status
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Host {{ var_fqdn }} {{ 'already exists' if host_exists_in_inventory else 'not found' }} in inventory"

    - name: Determine target group based on environment and location
      ansible.builtin.set_fact:
        target_group: "{{ var_dc_location.lower() }}_{{ var_environment.lower() }}"
        parent_groups:
          - "{{ var_environment.lower() }}"
          - "all"

    - name: Add new host to inventory dynamically
      ansible.builtin.add_host:
        name: "{{ var_fqdn }}"
        groups: "{{ target_group }}"
        ansible_host: "{{ var_prd_ip }}"
        ansible_user: "{{ var_ansible_user }}"
        ansible_ssh_private_key_file: "{{ var_ansible_ssh_private_key_file }}"
        ansible_python_interpreter: "{{ var_ansible_python_interpreter }}"
        ansible_ssh_timeout: "{{ var_ansible_ssh_timeout }}"
        ansible_command_timeout: "{{ var_ansible_command_timeout }}"
        # Host-specific variables
        PRD_IP: "{{ var_prd_ip }}"
        FQDN: "{{ var_fqdn }}"
        Environment: "{{ var_environment }}"
        dc_location: "{{ var_dc_location }}"
        pam_action: "{{ var_pam_action }}"
        debug_output: "{{ var_debug_output }}"
        ignore_errors: "{{ var_ignore_errors }}"
        filesystem_payload: "{{ var_filesystem_payload }}"
        # Mark as dynamically added
        dynamically_added: true
        added_timestamp: "{{ ansible_date_time.iso8601 }}"
      when: not host_exists_in_inventory

    - name: Log successful host addition
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Successfully added {{ var_fqdn }} to group {{ target_group }}"
      when: not host_exists_in_inventory

    - name: Test connectivity to newly added host
      ansible.builtin.wait_for:
        host: "{{ var_prd_ip }}"
        port: 22
        timeout: 60
        delay: 5
      register: connectivity_test
      failed_when: false

    - name: Handle connectivity failure
      ansible.builtin.fail:
        msg: "Cannot establish SSH connectivity to {{ var_fqdn }} ({{ var_prd_ip }}). Please verify network connectivity and SSH service."
      when:
        - connectivity_test.failed | default(false)
        - not (var_ignore_errors | default(false))

    - name: Log connectivity test result
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Connectivity test to {{ var_fqdn }}: {{ 'successful' if not (connectivity_test.failed | default(false)) else 'failed' }}"

    - name: Processing New VM AAP Inventory, Instance Group and Credentials selection
      ansible.builtin.command: ./files/set-aapreq.py
      register: aap_result
      failed_when: false
      changed_when: aap_result.rc == 0

- name: VM Post-Configuration Tasks for Dynamically Added Hosts
  hosts: "{{ hostvars['localhost']['var_fqdn'] }}"
  become: true
  gather_facts: true
  serial: 1

  vars:
    # Inherit variables from the dynamically added host
    var_fqdn: "{{ FQDN }}"
    var_prd_ip: "{{ PRD_IP }}"
    var_environment: "{{ Environment }}"
    var_pam_action: "{{ pam_action }}"
    var_dc_location: "{{ dc_location }}"
    var_debug_output: "{{ debug_output }}"
    var_ignore_errors: "{{ ignore_errors }}"
    var_filesystem_payload: "{{ filesystem_payload }}"

    # Logging configuration
    log_file: "/var/log/ansible-postconfig.log"

  pre_tasks:
    - name: Log start of configuration for target host
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Starting VM Post-Configuration for {{ var_fqdn }} on target host"
        create: true
        mode: '0644'
      delegate_to: localhost

    - name: Verify host variables are properly inherited
      ansible.builtin.debug:
        msg:
          - "Configuring host: {{ var_fqdn }}"
          - "IP Address: {{ var_prd_ip }}"
          - "Environment: {{ var_environment }}"
          - "DC Location: {{ var_dc_location }}"
          - "Dynamically Added: {{ dynamically_added | default(false) }}"
      when: var_debug_output | default(false)

  roles:
    - role: unix-postconfig
      vars:
        fqdn: "{{ var_fqdn }}"
        prd_ip: "{{ var_prd_ip }}"
        environment: "{{ var_environment }}"
        pam_action: "{{ var_pam_action }}"
        dc_location: "{{ var_dc_location }}"
        debug_output: "{{ var_debug_output }}"
        ignore_configuration_errors: "{{ var_ignore_errors }}"
        filesystem_payload: "{{ var_filesystem_payload }}"

  post_tasks:
    - name: Final logging for target host
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Completed VM Post-Configuration for {{ var_fqdn }} on target host"
      delegate_to: localhost

    - name: Update inventory status for dynamically added host
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Host {{ var_fqdn }} successfully configured and ready for production"
      delegate_to: localhost
      when: dynamically_added | default(false)

- name: Post-Configuration Summary and Cleanup
  hosts: localhost
  gather_facts: false
  connection: local

  vars:
    var_fqdn: "{{ FQDN | default('') }}"
    log_file: "/var/log/ansible-postconfig.log"

  tasks:
    - name: Generate configuration summary
      ansible.builtin.set_fact:
        configuration_summary:
          host: "{{ var_fqdn }}"
          timestamp: "{{ ansible_date_time.iso8601 }}"
          status: "completed"
          dynamically_added: "{{ hostvars[var_fqdn]['dynamically_added'] | default(false) }}"
          environment: "{{ hostvars[var_fqdn]['Environment'] | default('unknown') }}"
          dc_location: "{{ hostvars[var_fqdn]['dc_location'] | default('unknown') }}"

    - name: Log final summary
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - SUMMARY: {{ configuration_summary | to_json }}"

    - name: Display configuration summary
      ansible.builtin.debug:
        msg:
          - "=== VM Post-Configuration Summary ==="
          - "Host: {{ configuration_summary.host }}"
          - "Status: {{ configuration_summary.status }}"
          - "Environment: {{ configuration_summary.environment }}"
          - "DC Location: {{ configuration_summary.dc_location }}"
          - "Dynamically Added: {{ configuration_summary.dynamically_added }}"
          - "Completed: {{ configuration_summary.timestamp }}"
          - "==================================="

    - name: Optional - Remove host from in-memory inventory
      ansible.builtin.meta: clear_host_errors
      when:
        - hostvars[var_fqdn]['dynamically_added'] | default(false)
        - cleanup_dynamic_hosts | default(false)
