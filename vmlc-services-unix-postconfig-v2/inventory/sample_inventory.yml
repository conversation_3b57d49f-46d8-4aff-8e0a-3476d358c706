# Sample Inventory for Unix Post-Configuration v2
# YAML format inventory with groups and variables
# Supports both static hosts and dynamic host addition

all:
  children:
    production:
      children:
        hdc1_production:
          hosts:
            # Example static hosts (optional - can be empty for dynamic-only scenarios)
            server01.hcloud.healthgrp.com.sg:
              ansible_host: *************
              PRD_IP: *************
              FQDN: server01.hcloud.healthgrp.com.sg
              Environment: production
              dc_location: HDC1
              dynamically_added: false
            server02.hcloud.healthgrp.com.sg:
              ansible_host: *************
              PRD_IP: *************
              FQDN: server02.hcloud.healthgrp.com.sg
              Environment: production
              dc_location: HDC1
              dynamically_added: false
          vars:
            dc_location: HDC1
            Environment: production

        hdc2_production:
          hosts:
            server03.hcloud.healthgrp.com.sg:
              ansible_host: *************
              PRD_IP: *************
              FQDN: server03.hcloud.healthgrp.com.sg
              Environment: production
              dc_location: HDC2
            server04.hcloud.healthgrp.com.sg:
              ansible_host: *************
              PRD_IP: *************
              FQDN: server04.hcloud.healthgrp.com.sg
              Environment: production
              dc_location: HDC2
          vars:
            dc_location: HDC2
            Environment: production

    staging:
      children:
        hdc1_staging:
          hosts:
            stg-server01.hcloud.healthgrp.com.sg:
              ansible_host: **************
              PRD_IP: **************
              FQDN: stg-server01.hcloud.healthgrp.com.sg
              Environment: staging
              dc_location: HDC1
          vars:
            dc_location: HDC1
            Environment: staging

        hdc2_staging:
          hosts:
            stg-server02.hcloud.healthgrp.com.sg:
              ansible_host: **************
              PRD_IP: **************
              FQDN: stg-server02.hcloud.healthgrp.com.sg
              Environment: staging
              dc_location: HDC2
          vars:
            dc_location: HDC2
            Environment: staging

    development:
      children:
        hdc1_development:
          hosts:
            dev-server01.devhealthgrp.com.sg:
              ansible_host: **************
              PRD_IP: **************
              FQDN: dev-server01.devhealthgrp.com.sg
              Environment: development
              dc_location: HDC1
          vars:
            dc_location: HDC1
            Environment: development

  vars:
    # Global variables for all hosts
    ansible_user: ansible
    ansible_ssh_private_key_file: ~/.ssh/id_rsa
    ansible_python_interpreter: /usr/bin/python3

    # Default configuration values
    pam_action: add
    debug_output: false
    ignore_errors: false

    # Timeout settings
    ansible_ssh_timeout: 30
    ansible_command_timeout: 300
