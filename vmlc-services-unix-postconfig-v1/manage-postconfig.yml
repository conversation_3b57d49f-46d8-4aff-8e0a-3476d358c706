- name: VM Post-Configuration Tasks for Redhat and Oracle Linux
  hosts: all
  become: yes

  # @runtime extra variables to specify. Manual or Triggered from outside. Modify accordingly.

  # ansible-playbook -i hosts --ask-vault-pass syp_linux_postconfig.yml -e \
  # '{"FQDN": "HISANSVPAPP12.hcloud.healthgrp.com.sg", \
  # "PRD_IP": "**************", \
  # "Environment": "PRODuction", \
  # "pam_action":"add", \
  # "dc_location": "HDC2", \
  # "debug_output":false} '

  # NOTE! Any new VMs will be added to VMLIFECYCLE_SYP_H_CES_OR_INV in AAP.
  # TODO! Future enchancements to connect back to bitbucket or jira for single source of truth update.

  pre-tasks:
   - name: Processing New VM AAP Inventory, Instance Group and Credentials selection.
     command: ./files/set-aapreq.py
     delegate_to: localhost
     register: aap
     ignore_errors: yes

  tasks: 
    - name: Configuring hostname and /etc/hosts
      script: ./files/set-host.py {{ FQDN }} {{ PRD_IP }}
      register: host
      ignore_errors: yes
      tags: host
    - name: Debug output of hostname configuration
      debug:
        var: host.stdout_lines
      when: debug_output | default(false)
      tags: host

    - name: Reboot the server
      reboot:
        msg: "Reboot initiated by Ansible after hostname and /etc/hosts configuration."
      when: host is not failed

    - name: Configuring PAM Accounts for H-Cloud UNIX
      script: ./files/set-pam.py {{ Environment }}
      register: pam
      ignore_errors: yes
    - name: Debug output PAM Accounts Creation
      debug:
        var: pam.stdout_lines
      when: debug_output | default(false)

    - name: Configuring bp.conf
      script: ./files/set-bpconf.py
      register: bp
      ignore_errors: yes
    - name: Debug output of bp.conf configuration
      debug:
        var: bp.stdout_lines
      when: debug_output | default(false)

    - name: Configuring chrony.conf
      script: ./files/set-chrony.py {{ dc_location }}
      register: chrony
      ignore_errors: yes
    - name: Debug output of chrony.conf configuration
      debug:
        var: chrony.stdout_lines
      when: debug_output | default(false)

    - name: Configuring resolv.conf
      script: ./files/set-resolv.py {{ FQDN }} {{ dc_location }}
      register: dns
      ignore_errors: yes
    - name: Debug output of resolv.conf configuration
      debug:
        var: dns.stdout_lines
      when: debug_output | default(false)