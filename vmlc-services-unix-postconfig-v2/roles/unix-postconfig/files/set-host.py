#!/usr/bin/env python3
# Enhanced hostname and /etc/hosts configuration script
# Version 2.0 - Improved error handling, validation, and security

import subprocess
import sys
import re
import os
import json
from datetime import datetime

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")

def validate_hostname(hostname):
    """Validate hostname format according to RFC standards"""
    if not hostname or len(hostname) > 253:
        return False
    
    # RFC 1123 hostname validation
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return re.match(pattern, hostname) is not None

def validate_ip_address(ip):
    """Validate IPv4 address format"""
    pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    return re.match(pattern, ip) is not None

def get_current_hostname():
    """Get current system hostname"""
    try:
        result = subprocess.run(['hostnamectl', '--static'], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE, 
                              universal_newlines=True,
                              timeout=30)
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            log_message(f"Failed to get hostname: {result.stderr}", "ERROR")
            return None
    except subprocess.TimeoutExpired:
        log_message("Timeout getting current hostname", "ERROR")
        return None
    except Exception as e:
        log_message(f"Exception getting current hostname: {e}", "ERROR")
        return None

def change_hostname(new_hostname):
    """Change system hostname using hostnamectl"""
    try:
        current_hostname = get_current_hostname()
        if current_hostname == new_hostname:
            log_message(f"Hostname is already set to {new_hostname}. No changes needed.")
            return True

        log_message(f"Changing hostname from {current_hostname} to {new_hostname}")
        
        # Use hostnamectl without sudo since Ansible handles privilege escalation
        result = subprocess.run(['hostnamectl', 'set-hostname', new_hostname], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=60)
        
        if result.returncode == 0:
            # Verify the change
            verify_result = subprocess.run(['hostnamectl', '--static'], 
                                         stdout=subprocess.PIPE, 
                                         stderr=subprocess.PIPE,
                                         universal_newlines=True,
                                         timeout=30)
            
            if verify_result.returncode == 0:
                actual_hostname = verify_result.stdout.strip()
                if actual_hostname == new_hostname:
                    log_message(f"Hostname successfully changed to: {new_hostname}")
                    return True
                else:
                    log_message(f"Hostname verification failed. Expected: {new_hostname}, Got: {actual_hostname}", "ERROR")
                    return False
            else:
                log_message(f"Failed to verify hostname change: {verify_result.stderr}", "ERROR")
                return False
        else:
            log_message(f"Failed to change hostname: {result.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log_message("Timeout changing hostname", "ERROR")
        return False
    except Exception as e:
        log_message(f"Exception changing hostname: {e}", "ERROR")
        return False

def load_hosts_configuration():
    """Load hosts configuration from external source or use defaults"""
    # This would typically load from Ansible variables, but for now use embedded config
    return {
        "standard_entries": [
            "# Default Configuration for system to recognize and resolve localhost",
            "127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4",
            "::1         localhost localhost.localdomain localhost6 localhost6.localdomain6"
        ],
        "netbackup_entries": [
            "# NetBackup Master and Media Servers for Domain 2C",
            "**************  HISBKPVPMAS30",
            "**************  HISBKPPPMAS16",
            "**************  HISBKPPPMAS66",
            "# NetBackup Master and Media Servers for Domain 2B", 
            "*************   HISBKPVPMAS20",
            "*************   HISBKPPPMAS15",
            "# NetBackup Master and Media Servers for Domain 2",
            "*************   HISBKPVPMAS10",
            "*************   HISBKPPPMAS12"
        ],
        "infrastructure_entries": [
            "# Redhat Capsules",
            "************ hisrhcivpcap01.hcloud.healthgrp.com.sg hisrhcivpcap01",
            "************ hisrhcivpcap02.hcloud.healthgrp.com.sg hisrhcivpcap02",
            "# EG Manager",
            "************ HISIMTAVPWEB01.hcloud.healthgrp.com.sg HISIMTAVPWEB01",
            "************* HISIMTAVPWEB02.hcloud.healthgrp.com.sg HISIMTAVPWEB02"
        ]
    }

def backup_hosts_file():
    """Create backup of existing /etc/hosts file"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"/etc/hosts.backup.{timestamp}"
        
        if os.path.exists("/etc/hosts"):
            subprocess.run(['cp', '/etc/hosts', backup_path], check=True, timeout=30)
            log_message(f"Backed up /etc/hosts to {backup_path}")
            return backup_path
        else:
            log_message("/etc/hosts does not exist, no backup needed")
            return None
    except Exception as e:
        log_message(f"Failed to backup /etc/hosts: {e}", "ERROR")
        return None

def write_hosts_file(ip_address, new_hostname):
    """Write new /etc/hosts file with proper structure"""
    try:
        short_hostname = new_hostname.split('.')[0]
        hosts_config = load_hosts_configuration()
        
        # Create backup first
        backup_path = backup_hosts_file()
        
        # Build complete hosts file content
        all_entries = []
        all_entries.extend(hosts_config["standard_entries"])
        all_entries.extend(hosts_config["netbackup_entries"])
        all_entries.extend(hosts_config["infrastructure_entries"])
        all_entries.append("# LOCAL")
        all_entries.append(f"{ip_address} {new_hostname} {short_hostname}")
        
        # Write to temporary file first
        temp_file = "/tmp/hosts.new"
        with open(temp_file, 'w') as file:
            for entry in all_entries:
                file.write(entry + '\n')
        
        # Validate the temporary file
        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
            # Move to final location
            subprocess.run(['mv', temp_file, '/etc/hosts'], check=True, timeout=30)
            subprocess.run(['chmod', '644', '/etc/hosts'], check=True, timeout=30)
            log_message(f"/etc/hosts file updated successfully with hostname: {new_hostname} and IP: {ip_address}")
            return True
        else:
            log_message("Temporary hosts file validation failed", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"Failed to update /etc/hosts file: {e}", "ERROR")
        return False

def main():
    """Main function with enhanced error handling"""
    if len(sys.argv) != 3:
        log_message("Usage: python set-host.py <new_hostname> <ip_address>", "ERROR")
        sys.exit(1)

    new_hostname = sys.argv[1].strip()
    ip_address = sys.argv[2].strip()

    # Validate inputs
    if not validate_hostname(new_hostname):
        log_message(f"Invalid hostname format: {new_hostname}", "ERROR")
        sys.exit(1)

    if not validate_ip_address(ip_address):
        log_message(f"Invalid IP address format: {ip_address}", "ERROR")
        sys.exit(1)

    log_message(f"Starting hostname configuration: {new_hostname} ({ip_address})")

    # Change hostname
    hostname_success = change_hostname(new_hostname)
    
    # Update hosts file
    hosts_success = write_hosts_file(ip_address, new_hostname)

    # Report results
    if hostname_success and hosts_success:
        log_message("Hostname and hosts file configuration completed successfully")
        sys.exit(0)
    else:
        log_message("Hostname configuration failed", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
