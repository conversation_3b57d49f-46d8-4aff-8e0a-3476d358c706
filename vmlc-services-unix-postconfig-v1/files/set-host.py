#!/usr/bin/env python3

import subprocess
import sys

def get_current_hostname():
    try:
        result = subprocess.run(['hostnamectl'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        if result.returncode == 0:
            for line in result.stdout.splitlines():
                if "Static hostname" in line:
                    return line.split(":")[1].strip()
    except Exception as e:
        print(f"Failed to get current hostname. Error: {e}", file=sys.stderr)
    return None

def change_hostname(new_hostname):
    try:
        current_hostname = get_current_hostname()
        if current_hostname == new_hostname:
            print(f"Hostname is already set to {new_hostname}. No changes needed.")
            return

        # Execute the hostnamectl command to set the new hostname
        subprocess.run(['sudo', 'hostnamectl', 'set-hostname', new_hostname], check=True)

        # Verify the change
        result = subprocess.run(['hostnamectl'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        print(result.stdout)
        print(f"Hostname successfully changed to: {new_hostname}")
    except subprocess.CalledProcessError as e:
        print(f"Failed to change hostname. Error: {e}", file=sys.stderr)

def write_hosts_file(ip_address, new_hostname):
    try:
        short_hostname = new_hostname.split('.')[0]

        required_entries = [
            "# Default Configuration for system to recognize and resolve localhost",
            "127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4",
            "::1         localhost localhost.localdomain localhost6 localhost6.localdomain6",
            "# NetBackup Master and Media Servers for Domain 2C",
            "**************  HISBKPVPMAS30",
            "**************  HISBKPPPMAS16",
            "**************  HISBKPPPMAS66",
            "*************   HISBKPPPMED36",
            "*************   HISBKPPPMED37",
            "*************   HISBKPPPMED38",
            "*************   HISBKPPPMED39",
            "*************   HISBKPPPMED66",
            "*************   HISBKPPPMED67",
            "*************   HISBKPPPMED68",
            "*************   HISBKPPPMED69",
            "# NetBackup Master and Media Servers for Domain 2B",
            "*************   HISBKPVPMAS20",
            "*************   HISBKPPPMAS15",
            "*************   HISBKPPPMAS65",
            "************    HISBKPPPMED07",
            "***********     HISBKPPPMED31",
            "***********     HISBKPPPMED32",
            "************    HISBKPPPMED33",
            "************    HISBKPPPMED34",
            "10.247.86.198   HISBKPPPMED35",
            "10.247.66.181   HISBKPPPAPL07",
            "10.247.88.177   HISBKPPPMED57",
            "10.247.89.57    HISBKPPPMED61",
            "10.247.89.58    HISBKPPPMED62",
            "10.247.88.24    HISBKPPPMED63",
            "10.247.95.254   HISBKPPPMED64",
            "10.247.88.188   HISBKPPPMED65",
            "10.247.88.30    HISBKPPPAPL57",
            "# NetBackup Master and Media Servers for Domain 2",
            "10.247.122.63   HISBKPVPMAS10",
            "10.247.122.64   HISBKPPPMAS12",
            "10.247.122.65   HISBKPVPMAS13",
            "10.247.122.72   HISBKPPPMAS62",
            "10.247.122.73   HISBKPVPMAS63",
            "10.247.84.1     hisbkpppapl12",
            "10.247.84.2     hisbkpppapl13",
            "10.247.87.201   hisbkpppapl15",
            "10.247.87.202   hisbkpppapl16",
            "10.247.94.1     hisbkpppapl62",
            "10.247.94.2     hisbkpppapl63",
            "10.247.95.201   hisbkpppapl65",
            "10.247.95.202   hisbkpppapl66",
            "10.247.90.200   HISBKPPPAPL67",
            "# Redhat Capsules",
            "************ hisrhcivpcap01.hcloud.healthgrp.com.sg hisrhcivpcap01",
            "************ hisrhcivpcap02.hcloud.healthgrp.com.sg hisrhcivpcap02",
            "# EG Manager",
            "************ HISIMTAVPWEB01.hcloud.healthgrp.com.sg HISIMTAVPWEB01",
            "************* HISIMTAVPWEB02.hcloud.healthgrp.com.sg HISIMTAVPWEB02",
            "************ HISIMTAVSWEB01.hcloud.healthgrp.com.sg HISIMTAVSWEB01",
            "# LOCAL",
            f"{ip_address} {new_hostname} {short_hostname}"
        ]

        with open('/etc/hosts', 'w') as file:
            for entry in required_entries:
                file.write(entry + '\n')

        print(f"/etc/hosts file updated successfully with new hostname: {new_hostname} and IP: {ip_address}")
    except Exception as e:
        print(f"Failed to update /etc/hosts file. Error: {e}", file=sys.stderr)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python change_hostname.py <new_hostname> <ip_address>")
        sys.exit(1)

    new_hostname = sys.argv[1]
    ip_address = sys.argv[2]

    change_hostname(new_hostname)
    write_hosts_file(ip_address, new_hostname)

