# Hostname and /etc/hosts configuration tasks
# Enhanced with proper error handling and idempotency checks

- name: Log hostname configuration start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting hostname configuration for {{ service_fqdn }}"
  delegate_to: localhost

- name: Get current hostname
  ansible.builtin.command: hostnamectl --static
  register: current_hostname_result
  changed_when: false
  failed_when: false

- name: Check if hostname change is needed
  ansible.builtin.set_fact:
    hostname_change_needed: "{{ current_hostname_result.stdout.strip() != service_fqdn }}"

- name: Configure hostname and /etc/hosts
  ansible.builtin.script: "{{ role_path }}/files/set-host.py {{ service_fqdn }} {{ service_ip }}"
  register: hostname_result
  failed_when: false
  when: hostname_change_needed
  notify: 
    - reboot server
    - wait for server

- name: Handle hostname configuration failure
  ansible.builtin.fail:
    msg: "Hostname configuration failed: {{ hostname_result.stderr | default('Unknown error') }}"
  when: 
    - hostname_result is defined
    - hostname_result.rc != 0
    - not (ignore_configuration_errors | default(false))

- name: Log hostname configuration result
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Hostname configuration {{ 'completed' if hostname_result.rc == 0 else 'failed' }} for {{ service_fqdn }}"
  delegate_to: localhost
  when: hostname_result is defined

- name: Debug hostname configuration output
  ansible.builtin.debug:
    var: hostname_result.stdout_lines
  when: 
    - debug_output | default(false)
    - hostname_result is defined

- name: Set hostname change fact for reboot handler
  ansible.builtin.set_fact:
    hostname_changed: "{{ hostname_result is defined and hostname_result.rc == 0 }}"
