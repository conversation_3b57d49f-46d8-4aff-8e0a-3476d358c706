# Handlers for Unix Post-Configuration Role
# Enhanced with proper logging and conditional execution

- name: reboot server
  ansible.builtin.reboot:
    msg: "Reboot initiated by Ansible after hostname configuration."
    reboot_timeout: 600
    connect_timeout: 20
    test_command: uptime
  when: hostname_changed | default(false)
  listen: "reboot server"

- name: wait for server
  ansible.builtin.wait_for_connection:
    connect_timeout: 20
    sleep: 5
    delay: 5
    timeout: 300
  listen: "wait for server"

- name: log reboot
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Server {{ inventory_hostname }} rebooted successfully after hostname change"
  delegate_to: localhost
  listen: "reboot server"

- name: restart chronyd
  ansible.builtin.systemd:
    name: chronyd
    state: restarted
    enabled: true
  listen: "restart chronyd"

- name: restart network
  ansible.builtin.systemd:
    name: NetworkManager
    state: restarted
  listen: "restart network"
