# Main tasks for Unix Post-Configuration Role
# Orchestrates all configuration tasks with proper error handling and logging

- name: Log task start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting configuration tasks for {{ fqdn }}"
  delegate_to: localhost

- name: Include hostname configuration tasks
  ansible.builtin.include_tasks: configure-hostname.yml
  vars:
    service_name: "hostname"
    service_fqdn: "{{ fqdn }}"
    service_ip: "{{ prd_ip }}"
  tags: ['hostname', 'host']

- name: Include PAM accounts configuration tasks
  ansible.builtin.include_tasks: configure-pam.yml
  vars:
    service_name: "pam"
    service_environment: "{{ environment }}"
    service_action: "{{ pam_action }}"
  tags: ['pam', 'accounts']

- name: Include NetBackup configuration tasks
  ansible.builtin.include_tasks: configure-netbackup.yml
  vars:
    service_name: "netbackup"
  tags: ['netbackup', 'backup']

- name: Include Chrony configuration tasks
  ansible.builtin.include_tasks: configure-chrony.yml
  vars:
    service_name: "chrony"
    service_location: "{{ dc_location }}"
  tags: ['chrony', 'time', 'ntp']

- name: Include DNS configuration tasks
  ansible.builtin.include_tasks: configure-dns.yml
  vars:
    service_name: "dns"
    service_fqdn: "{{ fqdn }}"
    service_location: "{{ dc_location }}"
  tags: ['dns', 'resolv']

- name: Log task completion
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Completed configuration tasks for {{ fqdn }}"
  delegate_to: localhost
