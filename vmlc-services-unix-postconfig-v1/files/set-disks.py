import subprocess
import json
import os
import argparse
import socket

def run_command(command):
    """Run a shell command and return the output"""
    try:
        result = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(e.output)
        return ""

def get_all_disks():
    """Get all disks of type 'disk'"""
    return run_command("lsblk -dn -o NAME,TYPE | grep 'disk' | awk '{print $1}'").split()

def get_all_partitions():
    """Get all partitions"""
    return run_command("lsblk -dn -o NAME,TYPE | grep 'part' | awk '{print $1}'").split()

def get_mounted_disks_and_partitions():
    """Get all mounted disks and partitions"""
    return run_command("lsblk -ln -o NAME,MOUNTPOINT | awk '$2!=\"\" {print $1}'").split()

def is_disk_partitioned(disk):
    """Check if a disk has existing partitions"""
    partitions = run_command(f"lsblk -dn -o NAME,TYPE /dev/{disk} | grep 'part'").split()
    return len(partitions) > 0

def is_disk_mounted(disk):
    """Check if a disk is mounted"""
    mount_points = run_command(f"lsblk -ln -o NAME,MOUNTPOINT /dev/{disk} | awk '$2!=\"\" {{print $2}}'").split()
    return len(mount_points) > 0

def get_primary_disks():
    """Identify primary disks based on partitions and mount points"""
    primary_disks = set()
    all_disks = get_all_disks()
    all_partitions = get_all_partitions()
    mounted_disks_and_partitions = get_mounted_disks_and_partitions()
    
    for disk in all_disks:
        if any(part.startswith(disk) for part in all_partitions) or is_disk_mounted(disk):
            primary_disks.add(disk)
    
    return primary_disks

def get_unmounted_disks():
    """Identify unmounted disks with no existing partitions or filesystems"""
    all_disks = get_all_disks()
    primary_disks = get_primary_disks()
    
    # Identify unmounted disks with no existing partitions
    unmounted_disks = []
    for disk in all_disks:
        if disk not in primary_disks:
            unmounted_disks.append(f"/dev/{disk}")
    
    return unmounted_disks

def format_disk(disk):
    """Format the disk to create a new partition"""
    fdisk_command = f"echo -e 'n\np\n\n\n\nt\n8e\nw' | fdisk {disk}"
    output = run_command(fdisk_command)
    print(f"Formatting disk {disk}: {output}")
    return output

def create_mount_point(mount_point):
    """Create a new mount point directory"""
    if not os.path.exists(mount_point):
        mkdir_command = f"mkdir -p {mount_point}"
        output = run_command(mkdir_command)
        print(f"Creating mount point {mount_point}: {output}")
        return output
    else:
        print(f"Mount point {mount_point} already exists.")
        return ""

def create_partition(disk, mount_point, lv_name, vg_name):
    """Create a new partition and logical volume"""
    if run_command(f"pvs | grep -q {disk}1") == "":
        ssm_command = f"ssm create -s 100% -n {lv_name} --fstype xfs -p {vg_name} {disk}1 {mount_point}"
        output = run_command(ssm_command)
        print(f"Creating partition on {disk} and logical volume {lv_name} at {mount_point}: {output}")
        return output
    else:
        print(f"Partition on {disk} and logical volume {lv_name} already exists.")
        return ""

def add_to_fstab(vg_name, lv_name, mount_point):
    """Add the new logical volume to /etc/fstab"""
    fstab_entry = f"/dev/{vg_name}/{lv_name} {mount_point} xfs defaults 0 0\n"
    with open('/etc/fstab', 'r') as fstab:
        if fstab_entry.strip() in fstab.read():
            print(f"{mount_point} is already in /etc/fstab")
            return
    with open('/etc/fstab', 'a') as fstab:
        fstab.write(fstab_entry)
    print(f"Added {mount_point} to /etc/fstab")

def determine_lv_vg_names(hostname, disk_name):
    """Determine LV and VG names based on hostname"""
    lv_name = f"{hostname}_{disk_name}_lv"
    vg_name = f"{hostname}_{disk_name}_vg"
    return lv_name, vg_name

def configure_new_filesystems(payload, hostname):
    """Configure new filesystems based on the payload from Jira"""
    new_disks = get_unmounted_disks()
    if len(new_disks) < len(payload):
        print("Not enough unmounted disks available for the required configuration.")
        return

    for i, disk in enumerate(new_disks[:len(payload)]):
        data_drive = payload[i]
        mount_point = data_drive["Data Drive Name"]
        disk_name = disk.split('/')[-1]
        
        lv_name, vg_name = determine_lv_vg_names(hostname, disk_name)

        # Format the disk if not already partitioned
        if not is_disk_partitioned(disk_name):
            print(f"Formatting disk {disk}")
            format_output = format_disk(disk)
        else:
            print(f"Disk {disk} is already partitioned.")

        # Create the mount point if it does not exist
        print(f"Creating mount point {mount_point}")
        mkdir_output = create_mount_point(mount_point)

        # Create the partition and logical volume if not already created
        print(f"Creating partition on {disk} and logical volume {lv_name} at {mount_point}")
        ssm_output = create_partition(disk, mount_point, lv_name, vg_name)

        # Add to /etc/fstab if not already present
        add_to_fstab(vg_name, lv_name, mount_point)

def main():
    parser = argparse.ArgumentParser(description='Disk Management Script')
    parser.add_argument('--payload', type=str, required=True, help='JSON string of the payload')

    args = parser.parse_args()
    payload = json.loads(args.payload)
    hostname = socket.gethostname()
    
    configure_new_filesystems(payload, hostname)

    # Original functionality to list new unmounted disks
    new_disks = get_unmounted_disks()
    disks_info = []
    for disk in new_disks:
        disk_name = disk.split('/')[-1]
        disk_status = {
            "disk": disk,
            "partitioned": is_disk_partitioned(disk_name),
            "mounted": is_disk_mounted(disk_name),
            "actions": []
        }
        
        if not disk_status["partitioned"] and not disk_status["mounted"]:
            disk_status["actions"].append("unmounted and no existing partitions")
        elif disk_status["partitioned"]:
            disk_status["actions"].append("disk has partitions")
        elif disk_status["mounted"]:
            disk_status["actions"].append("disk is mounted")
        
        disks_info.append(disk_status)
    
    if disks_info:
        print(json.dumps(disks_info, indent=4))
    else:
        print("No new unmounted disks found.")

if __name__ == "__main__":
    main()

