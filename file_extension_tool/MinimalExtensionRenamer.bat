@echo off
setlocal enabledelayedexpansion

:: Minimal File Extension Renamer Tool
:: Author: <PERSON> - CES Operational Excellence Team
:: Description: A minimal batch script to rename file extensions to .txt and back

echo ===================================================
echo           MINIMAL FILE EXTENSION RENAMER
echo ===================================================
echo.
echo This tool will convert all file extensions to .txt
echo in the CURRENT DIRECTORY (where this script is located).
echo.
echo 1. To convert extensions to .txt, press C
echo 2. To restore original extensions, press R
echo 3. To exit, press any other key
echo.
echo ===================================================
echo.

set /p choice="Your choice (C/R): "

if /i "%choice%"=="C" goto convert
if /i "%choice%"=="R" goto restore
exit /b 0

:convert
echo.
echo Converting all file extensions to .txt in the current directory...
echo.

:: Create the extension map file
set "map_file=extension_map.txt"
if exist "%map_file%" del "%map_file%"

:: Process files in current directory
for %%f in (*) do (
    :: Skip this batch file and the map file
    if /i not "%%f"=="%~nx0" if /i not "%%f"=="%map_file%" (
        set "filename=%%~nf"
        set "extension=%%~xf"
        
        :: Skip if already .txt
        if /i not "!extension!"==".txt" (
            :: Store the mapping
            echo %%f=!extension! >> "%map_file%"
            
            :: Rename the file
            ren "%%f" "!filename!.txt" 2>nul
            if !errorlevel! equ 0 (
                echo Converted: %%f -^> !filename!.txt
            ) else (
                echo Error converting: %%f
            )
        )
    )
)

echo.
echo Conversion completed!
echo A mapping file has been created: %map_file%
echo.
pause
exit /b 0

:restore
echo.
echo Restoring original file extensions in the current directory...
echo.

:: Check if the map file exists
set "map_file=extension_map.txt"
if not exist "%map_file%" (
    echo Error: extension_map.txt not found in the current directory.
    echo Cannot restore original extensions without the mapping file.
    echo.
    pause
    exit /b 1
)

:: Process the map file
for /f "tokens=1,2 delims==" %%a in ('type "%map_file%"') do (
    set "filename=%%~na"
    
    :: Check if the .txt file exists
    if exist "!filename!.txt" (
        :: Rename the file back to its original extension
        ren "!filename!.txt" "%%a" 2>nul
        if !errorlevel! equ 0 (
            echo Restored: !filename!.txt -^> %%a
        ) else (
            echo Error restoring: !filename!.txt
        )
    ) else (
        echo Warning: File not found for restoration: !filename!.txt
    )
)

echo.
echo Restoration completed!
echo.
pause
exit /b 0
