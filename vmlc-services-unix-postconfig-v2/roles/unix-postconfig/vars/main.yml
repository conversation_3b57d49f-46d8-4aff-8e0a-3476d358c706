# Variables for Unix Post-Configuration Role
# Centralized configuration data moved from hardcoded Python scripts

# NetBackup server configurations
netbackup_servers:
  domain_2c:
    - "**************  HISBKPVPMAS30"
    - "**************  HISBKPPPMAS16"
    - "**************  HISBKPPPMAS66"
    - "*************   HISBK<PERSON>PMED36"
    - "*************   HISBKPPPMED37"
    - "*************   HISBKPPPMED38"
    - "*************   HISBKPPPMED39"
    - "*************   HISBKPPPMED66"
    - "*************   HISBKPPPMED67"
    - "*************   HISBKPPPMED68"
    - "*************   HISBKPPPMED69"
  
  domain_2b:
    - "*************   HISBKPVPMAS20"
    - "*************   HISBKPPPMAS15"
    - "*************   HISBKPPPMAS65"
    - "************    HISBKPPPMED07"
    - "***********     HISBKPPPMED31"
    - "***********     HISB<PERSON>PPPMED32"
    - "************    HISBK<PERSON><PERSON>ED33"
    - "************    HISBKPPPMED34"
    - "*************   HISBKPPPMED35"
    - "*************   HISBKPPPAPL07"
    - "*************   HISBKPPPMED57"
    - "************    HISBKPPPMED61"
    - "10.247.89.58    HISBKPPPMED62"
    - "10.247.88.24    HISBKPPPMED63"
    - "10.247.95.254   HISBKPPPMED64"
    - "10.247.88.188   HISBKPPPMED65"
    - "10.247.88.30    HISBKPPPAPL57"
  
  domain_2:
    - "10.247.122.63   HISBKPVPMAS10"
    - "*************   HISBKPPPMAS12"
    - "*************   HISBKPVPMAS13"
    - "*************   HISBKPPPMAS62"
    - "*************   HISBKPVPMAS63"
    - "***********     hisbkpppapl12"
    - "***********     hisbkpppapl13"
    - "*************   hisbkpppapl15"
    - "*************   hisbkpppapl16"
    - "***********     hisbkpppapl62"
    - "***********     hisbkpppapl63"
    - "*************   hisbkpppapl65"
    - "*************   hisbkpppapl66"
    - "*************   HISBKPPPAPL67"

# Standard /etc/hosts entries
standard_hosts_entries:
  - "# Default Configuration for system to recognize and resolve localhost"
  - "127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4"
  - "::1         localhost localhost.localdomain localhost6 localhost6.localdomain6"

# RedHat Capsule servers
redhat_capsules:
  - "************ hisrhcivpcap01.hcloud.healthgrp.com.sg hisrhcivpcap01"
  - "************ hisrhcivpcap02.hcloud.healthgrp.com.sg hisrhcivpcap02"

# EG Manager servers
eg_managers:
  - "************ HISIMTAVPWEB01.hcloud.healthgrp.com.sg HISIMTAVPWEB01"
  - "************* HISIMTAVPWEB02.hcloud.healthgrp.com.sg HISIMTAVPWEB02"
  - "************ HISIMTAVSWEB01.hcloud.healthgrp.com.sg HISIMTAVSWEB01"

# NTP server configurations
ntp_servers:
  HDC1:
    - "##HDC1 AMK NTP servers"
    - "server *********** iburst"
    - "server *********** iburst"
    - "##HDC2 FORT NTP servers"
    - "server ************ iburst"
    - "server ************ iburst"
  
  HDC2:
    - "##HDC2 FORT NTP servers"
    - "server ************ iburst"
    - "server ************ iburst"
    - "##HDC1 AMK NTP servers"
    - "server *********** iburst"
    - "server *********** iburst"

# PAM user configurations
pam_users:
  staging:
    ihisadm: 
      - pamunixadm2001
      - pamunixadm2002
      - pamunixadm2003
      - pamunixadm2004
      - pamunixadm2005
      - pamunixadm2006
    pamunixread:
      - pamunixread2001
    pamunixdeadm:
      - pamunixadm2011
      - pamunixadm2012
    pamunixderead:
      - pamunixread2011
      - pamunixread2012
  
  production:
    ihisadm:
      - pamunixadm1001
      - pamunixadm1002
      - pamunixadm1003
      - pamunixadm1004
      - pamunixadm1005
      - pamunixadm1006
    pamunixread:
      - pamunixread1001
    pamunixdeadm:
      - pamunixadm1011
      - pamunixadm1012
    pamunixderead:
      - pamunixread1011
      - pamunixread1012

# Logging configuration
log_file: "/var/log/ansible-postconfig.log"
log_rotation_size: "10M"
log_retention_days: 30

# Timeout configurations
reboot_timeout: 600
connection_timeout: 300
service_restart_timeout: 60
