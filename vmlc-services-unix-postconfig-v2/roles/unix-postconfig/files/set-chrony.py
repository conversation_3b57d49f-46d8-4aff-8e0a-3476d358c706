#!/usr/bin/env python3
# Enhanced Chrony NTP configuration script
# Version 2.0 - Improved error handling, validation, and security

import sys
import os
import subprocess
from datetime import datetime

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")

def validate_dc_location(location):
    """Validate data center location parameter"""
    valid_locations = ['HDC1', 'HDC2']
    return location.upper() in valid_locations

def load_ntp_configuration():
    """Load NTP server configuration for different data centers"""
    return {
        'HDC1': [
            '##HDC1 AMK NTP servers',
            'server *********** iburst',
            'server *********** iburst',
            '##HDC2 FORT NTP servers',
            'server ************ iburst',
            'server ************ iburst',
        ],
        'HDC2': [
            '##HDC2 FORT NTP servers',
            'server ************ iburst',
            'server ************ iburst',
            '##HDC1 AMK NTP servers',
            'server *********** iburst',
            'server *********** iburst',
        ]
    }

def backup_chrony_conf(file_path):
    """Create backup of existing chrony.conf file"""
    try:
        if os.path.exists(file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{file_path}.backup.{timestamp}"
            
            subprocess.run(['cp', file_path, backup_path], 
                         check=True, 
                         timeout=30)
            log_message(f"Backed up existing chrony.conf to {backup_path}")
            return backup_path
        else:
            log_message("No existing chrony.conf file to backup")
            return None
            
    except Exception as e:
        log_message(f"Failed to backup chrony.conf: {e}", "ERROR")
        return None

def read_existing_chrony_conf(file_path):
    """Read existing chrony.conf and filter out NTP server entries"""
    try:
        if not os.path.exists(file_path):
            log_message("chrony.conf does not exist, will create new one")
            return []
        
        with open(file_path, 'r') as file:
            lines = file.readlines()
        
        # Get all NTP lines to remove (from both HDC1 and HDC2 configs)
        ntp_config = load_ntp_configuration()
        lines_to_remove = set()
        
        for location_lines in ntp_config.values():
            for line in location_lines:
                lines_to_remove.add(line.strip())
        
        # Filter out existing NTP configuration
        filtered_lines = []
        in_ntp_section = False
        
        for line in lines:
            stripped_line = line.strip()
            
            # Skip lines that match our NTP configuration
            if stripped_line in lines_to_remove:
                continue
                
            # Skip comment lines that start our NTP sections
            if stripped_line.startswith('##HDC1') or stripped_line.startswith('##HDC2'):
                in_ntp_section = True
                continue
                
            # Skip server lines in NTP sections
            if in_ntp_section and stripped_line.startswith('server'):
                continue
                
            # End of NTP section
            if in_ntp_section and not stripped_line.startswith('server') and stripped_line:
                in_ntp_section = False
            
            # Keep all other lines
            if not in_ntp_section:
                filtered_lines.append(line)
        
        return filtered_lines
        
    except Exception as e:
        log_message(f"Failed to read existing chrony.conf: {e}", "ERROR")
        return []

def find_insertion_point(lines):
    """Find the best place to insert NTP server configuration"""
    try:
        # Look for the pool.ntp.org comment line
        for i, line in enumerate(lines):
            if '# Please consider joining the pool (http://www.pool.ntp.org/join.html).' in line:
                return i + 1
        
        # If not found, look for any pool or server line
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('pool ') or stripped.startswith('server '):
                return i
        
        # If no pool/server lines found, insert after any existing comments
        for i, line in enumerate(lines):
            if not line.strip().startswith('#') and line.strip():
                return i
        
        # Default to end of file
        return len(lines)
        
    except Exception as e:
        log_message(f"Exception finding insertion point: {e}", "ERROR")
        return len(lines)

def write_chrony_conf(file_path, dc_location):
    """Write new chrony.conf with NTP server configuration"""
    try:
        ntp_config = load_ntp_configuration()
        dc_location = dc_location.upper()
        
        if dc_location not in ntp_config:
            log_message(f"Invalid DC location: {dc_location}", "ERROR")
            return False
        
        # Read existing configuration
        existing_lines = read_existing_chrony_conf(file_path)
        
        # Get NTP lines for this location
        ntp_lines = ntp_config[dc_location]
        
        # Find insertion point
        insertion_point = find_insertion_point(existing_lines)
        
        # Build new configuration
        new_lines = existing_lines[:insertion_point]
        
        # Add NTP configuration
        for ntp_line in ntp_lines:
            new_lines.append(ntp_line + '\n')
        
        # Add remaining lines
        new_lines.extend(existing_lines[insertion_point:])
        
        # Write to temporary file first
        temp_file = f"{file_path}.tmp"
        with open(temp_file, 'w') as file:
            file.writelines(new_lines)
        
        # Validate temporary file
        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
            # Move to final location
            subprocess.run(['mv', temp_file, file_path], 
                         check=True, 
                         timeout=30)
            subprocess.run(['chmod', '644', file_path], 
                         check=True, 
                         timeout=30)
            log_message(f"Successfully updated chrony.conf for {dc_location}")
            return True
        else:
            log_message("Temporary chrony.conf file validation failed", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"Failed to write chrony.conf: {e}", "ERROR")
        return False

def verify_chrony_conf(file_path, dc_location):
    """Verify chrony.conf was configured correctly"""
    try:
        if not os.path.exists(file_path):
            log_message("chrony.conf file does not exist after creation", "ERROR")
            return False
        
        with open(file_path, 'r') as file:
            content = file.read()
        
        ntp_config = load_ntp_configuration()
        expected_servers = ntp_config[dc_location.upper()]
        
        # Check for expected server entries
        missing_servers = []
        for server_line in expected_servers:
            if server_line.startswith('server ') and server_line not in content:
                missing_servers.append(server_line)
        
        if missing_servers:
            log_message(f"Missing NTP servers in chrony.conf: {missing_servers}", "ERROR")
            return False
        
        # Count server entries
        server_count = content.count('server ')
        log_message(f"chrony.conf verification successful: {server_count} NTP servers configured")
        return True
        
    except Exception as e:
        log_message(f"Failed to verify chrony.conf: {e}", "ERROR")
        return False

def test_chrony_syntax(file_path):
    """Test chrony configuration syntax"""
    try:
        # Use chronyd to test configuration syntax
        result = subprocess.run(['chronyd', '-t', '-f', file_path], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=30)
        
        if result.returncode == 0:
            log_message("Chrony configuration syntax test passed")
            return True
        else:
            log_message(f"Chrony configuration syntax test failed: {result.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log_message("Timeout testing chrony configuration syntax", "ERROR")
        return False
    except FileNotFoundError:
        log_message("chronyd not found, skipping syntax test", "WARNING")
        return True  # Don't fail if chronyd is not available
    except Exception as e:
        log_message(f"Exception testing chrony syntax: {e}", "ERROR")
        return False

def main():
    """Main function with enhanced error handling"""
    if len(sys.argv) != 2:
        log_message("Usage: python set-chrony.py <HDC1|HDC2>", "ERROR")
        sys.exit(1)

    dc_location = sys.argv[1].strip().upper()
    file_path = "/etc/chrony.conf"

    # Validate input
    if not validate_dc_location(dc_location):
        log_message(f"Invalid DC location: {dc_location}. Must be HDC1 or HDC2", "ERROR")
        sys.exit(1)

    log_message(f"Starting Chrony configuration for {dc_location}")

    # Create backup
    backup_chrony_conf(file_path)

    # Write new configuration
    if not write_chrony_conf(file_path, dc_location):
        log_message("Failed to write chrony.conf", "ERROR")
        sys.exit(1)

    # Verify configuration
    if not verify_chrony_conf(file_path, dc_location):
        log_message("chrony.conf verification failed", "ERROR")
        sys.exit(1)

    # Test syntax
    if not test_chrony_syntax(file_path):
        log_message("chrony.conf syntax test failed", "ERROR")
        sys.exit(1)

    log_message("Chrony configuration completed successfully")
    sys.exit(0)

if __name__ == "__main__":
    main()
