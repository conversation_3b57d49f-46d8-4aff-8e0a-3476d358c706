# Chrony NTP configuration tasks
# Enhanced with proper error handling and service management

- name: Log Chrony configuration start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting Chrony configuration for {{ service_location }}"
  delegate_to: localhost

- name: Validate DC location parameter
  ansible.builtin.assert:
    that:
      - service_location in ['HDC1', 'HDC2']
    fail_msg: "DC location must be 'HDC1' or 'HDC2', got: {{ service_location }}"
    success_msg: "DC location validated: {{ service_location }}"

- name: Check if chrony.conf exists
  ansible.builtin.stat:
    path: /etc/chrony.conf
  register: chrony_conf_file

- name: Backup existing chrony.conf
  ansible.builtin.copy:
    src: /etc/chrony.conf
    dest: "/etc/chrony.conf.backup.{{ ansible_date_time.epoch }}"
    remote_src: true
    mode: '0644'
  when: chrony_conf_file.stat.exists

- name: Configure chrony.conf
  ansible.builtin.script: "{{ role_path }}/files/set-chrony.py {{ service_location }}"
  register: chrony_result
  failed_when: false

- name: Handle Chrony configuration failure
  ansible.builtin.fail:
    msg: "Chrony configuration failed: {{ chrony_result.stderr | default('Unknown error') }}"
  when: 
    - chrony_result.rc != 0
    - not (ignore_configuration_errors | default(false))

- name: Log Chrony configuration result
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Chrony configuration {{ 'completed' if chrony_result.rc == 0 else 'failed' }} for {{ service_location }}"
  delegate_to: localhost

- name: Debug Chrony configuration output
  ansible.builtin.debug:
    var: chrony_result.stdout_lines
  when: debug_output | default(false)

- name: Restart chronyd service
  ansible.builtin.systemd:
    name: chronyd
    state: restarted
    enabled: true
  when: chrony_result.rc == 0
  register: chronyd_restart

- name: Verify chronyd service status
  ansible.builtin.systemd:
    name: chronyd
  register: chronyd_status

- name: Log chronyd service status
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - chronyd service status: {{ chronyd_status.status.ActiveState | default('unknown') }}"
  delegate_to: localhost
