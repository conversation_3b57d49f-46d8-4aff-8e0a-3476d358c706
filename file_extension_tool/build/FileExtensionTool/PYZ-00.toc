('/home/<USER>/Documents/CodeRepository/file_extension_tool/build/FileExtensionTool/PYZ-00.pyz',
 [('__future__', '/usr/lib64/python3.13/__future__.py', 'PYMODULE'),
  ('_colorize', '/usr/lib64/python3.13/_colorize.py', 'PYMODULE'),
  ('_compat_pickle', '/usr/lib64/python3.13/_compat_pickle.py', 'PYMODULE'),
  ('_compression', '/usr/lib64/python3.13/_compression.py', 'PYMODULE'),
  ('_opcode_metadata', '/usr/lib64/python3.13/_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', '/usr/lib64/python3.13/_py_abc.py', 'PYMODULE'),
  ('_pydatetime', '/usr/lib64/python3.13/_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', '/usr/lib64/python3.13/_pydecimal.py', 'PYMODULE'),
  ('_strptime', '/usr/lib64/python3.13/_strptime.py', 'PYMODULE'),
  ('_threading_local', '/usr/lib64/python3.13/_threading_local.py', 'PYMODULE'),
  ('argparse', '/usr/lib64/python3.13/argparse.py', 'PYMODULE'),
  ('ast', '/usr/lib64/python3.13/ast.py', 'PYMODULE'),
  ('base64', '/usr/lib64/python3.13/base64.py', 'PYMODULE'),
  ('bisect', '/usr/lib64/python3.13/bisect.py', 'PYMODULE'),
  ('bz2', '/usr/lib64/python3.13/bz2.py', 'PYMODULE'),
  ('calendar', '/usr/lib64/python3.13/calendar.py', 'PYMODULE'),
  ('contextlib', '/usr/lib64/python3.13/contextlib.py', 'PYMODULE'),
  ('contextvars', '/usr/lib64/python3.13/contextvars.py', 'PYMODULE'),
  ('copy', '/usr/lib64/python3.13/copy.py', 'PYMODULE'),
  ('csv', '/usr/lib64/python3.13/csv.py', 'PYMODULE'),
  ('dataclasses', '/usr/lib64/python3.13/dataclasses.py', 'PYMODULE'),
  ('datetime', '/usr/lib64/python3.13/datetime.py', 'PYMODULE'),
  ('decimal', '/usr/lib64/python3.13/decimal.py', 'PYMODULE'),
  ('dis', '/usr/lib64/python3.13/dis.py', 'PYMODULE'),
  ('email', '/usr/lib64/python3.13/email/__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   '/usr/lib64/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/lib64/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', '/usr/lib64/python3.13/email/_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   '/usr/lib64/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime', '/usr/lib64/python3.13/email/base64mime.py', 'PYMODULE'),
  ('email.charset', '/usr/lib64/python3.13/email/charset.py', 'PYMODULE'),
  ('email.contentmanager',
   '/usr/lib64/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders', '/usr/lib64/python3.13/email/encoders.py', 'PYMODULE'),
  ('email.errors', '/usr/lib64/python3.13/email/errors.py', 'PYMODULE'),
  ('email.feedparser', '/usr/lib64/python3.13/email/feedparser.py', 'PYMODULE'),
  ('email.generator', '/usr/lib64/python3.13/email/generator.py', 'PYMODULE'),
  ('email.header', '/usr/lib64/python3.13/email/header.py', 'PYMODULE'),
  ('email.headerregistry',
   '/usr/lib64/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators', '/usr/lib64/python3.13/email/iterators.py', 'PYMODULE'),
  ('email.message', '/usr/lib64/python3.13/email/message.py', 'PYMODULE'),
  ('email.parser', '/usr/lib64/python3.13/email/parser.py', 'PYMODULE'),
  ('email.policy', '/usr/lib64/python3.13/email/policy.py', 'PYMODULE'),
  ('email.quoprimime', '/usr/lib64/python3.13/email/quoprimime.py', 'PYMODULE'),
  ('email.utils', '/usr/lib64/python3.13/email/utils.py', 'PYMODULE'),
  ('fnmatch', '/usr/lib64/python3.13/fnmatch.py', 'PYMODULE'),
  ('fractions', '/usr/lib64/python3.13/fractions.py', 'PYMODULE'),
  ('getopt', '/usr/lib64/python3.13/getopt.py', 'PYMODULE'),
  ('gettext', '/usr/lib64/python3.13/gettext.py', 'PYMODULE'),
  ('glob', '/usr/lib64/python3.13/glob.py', 'PYMODULE'),
  ('gzip', '/usr/lib64/python3.13/gzip.py', 'PYMODULE'),
  ('hashlib', '/usr/lib64/python3.13/hashlib.py', 'PYMODULE'),
  ('importlib', '/usr/lib64/python3.13/importlib/__init__.py', 'PYMODULE'),
  ('importlib._abc', '/usr/lib64/python3.13/importlib/_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/lib64/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/lib64/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', '/usr/lib64/python3.13/importlib/abc.py', 'PYMODULE'),
  ('importlib.machinery',
   '/usr/lib64/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/lib64/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/lib64/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/lib64/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/lib64/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/lib64/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/lib64/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/lib64/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/lib64/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/lib64/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/lib64/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/lib64/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/usr/lib64/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/lib64/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/lib64/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/lib64/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util', '/usr/lib64/python3.13/importlib/util.py', 'PYMODULE'),
  ('inspect', '/usr/lib64/python3.13/inspect.py', 'PYMODULE'),
  ('ipaddress', '/usr/lib64/python3.13/ipaddress.py', 'PYMODULE'),
  ('json', '/usr/lib64/python3.13/json/__init__.py', 'PYMODULE'),
  ('json.decoder', '/usr/lib64/python3.13/json/decoder.py', 'PYMODULE'),
  ('json.encoder', '/usr/lib64/python3.13/json/encoder.py', 'PYMODULE'),
  ('json.scanner', '/usr/lib64/python3.13/json/scanner.py', 'PYMODULE'),
  ('logging', '/usr/lib64/python3.13/logging/__init__.py', 'PYMODULE'),
  ('lzma', '/usr/lib64/python3.13/lzma.py', 'PYMODULE'),
  ('numbers', '/usr/lib64/python3.13/numbers.py', 'PYMODULE'),
  ('opcode', '/usr/lib64/python3.13/opcode.py', 'PYMODULE'),
  ('pathlib', '/usr/lib64/python3.13/pathlib/__init__.py', 'PYMODULE'),
  ('pathlib._abc', '/usr/lib64/python3.13/pathlib/_abc.py', 'PYMODULE'),
  ('pathlib._local', '/usr/lib64/python3.13/pathlib/_local.py', 'PYMODULE'),
  ('pickle', '/usr/lib64/python3.13/pickle.py', 'PYMODULE'),
  ('pprint', '/usr/lib64/python3.13/pprint.py', 'PYMODULE'),
  ('py_compile', '/usr/lib64/python3.13/py_compile.py', 'PYMODULE'),
  ('quopri', '/usr/lib64/python3.13/quopri.py', 'PYMODULE'),
  ('random', '/usr/lib64/python3.13/random.py', 'PYMODULE'),
  ('selectors', '/usr/lib64/python3.13/selectors.py', 'PYMODULE'),
  ('shutil', '/usr/lib64/python3.13/shutil.py', 'PYMODULE'),
  ('signal', '/usr/lib64/python3.13/signal.py', 'PYMODULE'),
  ('socket', '/usr/lib64/python3.13/socket.py', 'PYMODULE'),
  ('statistics', '/usr/lib64/python3.13/statistics.py', 'PYMODULE'),
  ('string', '/usr/lib64/python3.13/string.py', 'PYMODULE'),
  ('stringprep', '/usr/lib64/python3.13/stringprep.py', 'PYMODULE'),
  ('subprocess', '/usr/lib64/python3.13/subprocess.py', 'PYMODULE'),
  ('tarfile', '/usr/lib64/python3.13/tarfile.py', 'PYMODULE'),
  ('tempfile', '/usr/lib64/python3.13/tempfile.py', 'PYMODULE'),
  ('textwrap', '/usr/lib64/python3.13/textwrap.py', 'PYMODULE'),
  ('threading', '/usr/lib64/python3.13/threading.py', 'PYMODULE'),
  ('tkinter', '/usr/lib64/python3.13/tkinter/__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   '/usr/lib64/python3.13/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/usr/lib64/python3.13/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter.dialog', '/usr/lib64/python3.13/tkinter/dialog.py', 'PYMODULE'),
  ('tkinter.filedialog',
   '/usr/lib64/python3.13/tkinter/filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/usr/lib64/python3.13/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   '/usr/lib64/python3.13/tkinter/simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', '/usr/lib64/python3.13/tkinter/ttk.py', 'PYMODULE'),
  ('token', '/usr/lib64/python3.13/token.py', 'PYMODULE'),
  ('tokenize', '/usr/lib64/python3.13/tokenize.py', 'PYMODULE'),
  ('tracemalloc', '/usr/lib64/python3.13/tracemalloc.py', 'PYMODULE'),
  ('typing', '/usr/lib64/python3.13/typing.py', 'PYMODULE'),
  ('urllib', '/usr/lib64/python3.13/urllib/__init__.py', 'PYMODULE'),
  ('urllib.parse', '/usr/lib64/python3.13/urllib/parse.py', 'PYMODULE'),
  ('zipfile', '/usr/lib64/python3.13/zipfile/__init__.py', 'PYMODULE'),
  ('zipfile._path',
   '/usr/lib64/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/lib64/python3.13/zipfile/_path/glob.py',
   'PYMODULE')])
