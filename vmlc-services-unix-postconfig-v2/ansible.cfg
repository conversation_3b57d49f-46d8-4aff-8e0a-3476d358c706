# Ansible Configuration for Unix Post-Configuration v2
# Enhanced configuration for AAP compatibility and best practices

[defaults]
# Basic settings
inventory = inventory
host_key_checking = False
timeout = 30
gathering = smart
fact_caching = memory
fact_caching_timeout = 86400

# Output and logging
stdout_callback = yaml
bin_ansible_callbacks = True
display_skipped_hosts = False
display_ok_hosts = True
log_path = ./ansible.log

# Performance settings
forks = 10
poll_interval = 15
internal_poll_interval = 0.001

# Security settings
vault_password_file = .vault_pass
vault_encrypt_identity = default

# Role and collection paths
roles_path = ./roles:~/.ansible/roles:/usr/share/ansible/roles:/etc/ansible/roles
collections_path = ./collections:~/.ansible/collections:/usr/share/ansible/collections

# Retry settings
retry_files_enabled = True
retry_files_save_path = ./retry

# Module settings
module_utils = ./module_utils
action_plugins = ./action_plugins
lookup_plugins = ./lookup_plugins
filter_plugins = ./filter_plugins

[inventory]
# Inventory settings
enable_plugins = host_list, script, auto, yaml, ini, toml
cache = True
cache_plugin = memory
cache_timeout = 3600

[privilege_escalation]
# Privilege escalation settings
become = True
become_method = sudo
become_user = root
become_ask_pass = False

[paramiko_connection]
# Paramiko connection settings
record_host_keys = False
pty = False

[ssh_connection]
# SSH connection settings
ssh_args = -C -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no
pipelining = True
control_path_dir = /tmp/.ansible-cp
control_path = %(directory)s/%%h-%%p-%%r
scp_if_ssh = smart
transfer_method = smart
retries = 3

[persistent_connection]
# Persistent connection settings
connect_timeout = 30
connect_retry_timeout = 15
connect_interval = 1

[colors]
# Color settings for output
highlight = white
verbose = blue
warn = bright purple
error = red
debug = dark gray
deprecate = purple
skip = cyan
unreachable = red
ok = green
changed = yellow
diff_add = green
diff_remove = red
diff_lines = cyan
