# PAM accounts configuration tasks
# Enhanced with proper error handling and validation

- name: Log PAM configuration start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting PAM accounts configuration for {{ service_environment }}"
  delegate_to: localhost

- name: Validate PAM action parameter
  ansible.builtin.assert:
    that:
      - service_action in ['add', 'delete']
    fail_msg: "PAM action must be 'add' or 'delete', got: {{ service_action }}"
    success_msg: "PAM action validated: {{ service_action }}"

- name: Configure PAM accounts for H-Cloud UNIX
  ansible.builtin.script: "{{ role_path }}/files/set-pam.py {{ service_environment }} {{ service_action }}"
  register: pam_result
  failed_when: false

- name: Handle PAM configuration failure
  ansible.builtin.fail:
    msg: "PAM configuration failed: {{ pam_result.stderr | default('Unknown error') }}"
  when: 
    - pam_result.rc != 0
    - not (ignore_configuration_errors | default(false))

- name: Log PAM configuration result
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - PAM configuration {{ 'completed' if pam_result.rc == 0 else 'failed' }} for {{ service_environment }}"
  delegate_to: localhost

- name: Debug PAM accounts creation output
  ansible.builtin.debug:
    var: pam_result.stdout_lines
  when: debug_output | default(false)

- name: Verify PAM accounts were created (when adding)
  ansible.builtin.shell: |
    getent passwd | grep -c pamunix || echo "0"
  register: pam_account_count
  changed_when: false
  when: service_action == 'add'

- name: Log PAM account verification
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - PAM accounts verification: {{ pam_account_count.stdout | default('0') }} pamunix accounts found"
  delegate_to: localhost
  when: service_action == 'add' and pam_account_count is defined
