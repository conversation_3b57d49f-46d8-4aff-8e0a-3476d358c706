#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status

echo "Building File Extension Tool executable for Linux..."

# Check Python version
PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
echo "Detected Python version: $PYTHON_VERSION"

# Create virtual environment (optional but recommended)
echo "Creating virtual environment..."
python3 -m venv venv || {
    echo "Failed to create virtual environment. Trying to continue without it..."
    # If venv creation fails, try to use the system Python
    USING_SYSTEM_PYTHON=1
}

# Activate virtual environment if it was created
if [ -z "$USING_SYSTEM_PYTHON" ]; then
    source venv/bin/activate || {
        echo "Failed to activate virtual environment. Trying to continue without it..."
        USING_SYSTEM_PYTHON=1
    }
fi

# Install required packages
echo "Installing required packages..."
pip install --upgrade pip || echo "Failed to upgrade pip, continuing with existing version"
pip install -r requirements.txt || {
    echo "Failed to install packages from requirements.txt"
    echo "Trying to install PyInstaller directly..."
    pip install pyinstaller || {
        echo "ERROR: Failed to install PyInstaller. Build cannot continue."
        exit 1
    }
}

# Create dist directory if it doesn't exist
mkdir -p dist

# Create executable with PyInstaller
echo "Building executable with PyInstaller..."
pyinstaller --onefile --windowed --name="FileExtensionTool" extension_tool.py || {
    echo "ERROR: PyInstaller failed to build the executable."
    exit 1
}

# Make the executable executable if it exists
if [ -f "dist/FileExtensionTool" ]; then
    echo "Setting permissions..."
    chmod +x dist/FileExtensionTool
    echo "Build complete! Executable is in the dist folder."
    echo "You can run it with: ./dist/FileExtensionTool"
else
    echo "ERROR: Build failed. The executable was not created."
    exit 1
fi

# Deactivate virtual environment if we were using one
if [ -z "$USING_SYSTEM_PYTHON" ]; then
    deactivate || echo "Note: Failed to deactivate virtual environment"
fi

echo "Build process completed successfully."
