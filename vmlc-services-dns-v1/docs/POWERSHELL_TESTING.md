# PowerShell Script Testing Guide for DNS Engineers

## 1. Introduction

This document provides detailed instructions for DNS engineers on how to directly test and use the enhanced `set-dns.ps1` PowerShell script on ADMT servers. The script now features intelligent PTR zone detection and safe automatic PTR removal capabilities. This approach allows for quick testing and troubleshooting without going through the Ansible Automation Platform (AAP).

**Author:** CES Operational Excellence Team
**Contributor:** Muhammad <PERSON> (7409)

## 1.1 New Features in Enhanced Script

- **Intelligent PTR Zone Detection**: 3-tier hierarchical fallback (3→2→1 octet zones)
- **Safe PTR Removal**: Only removes PTR records that match the hostname being removed
- **Timeout Protection**: 10-second timeout for DNS queries to prevent hanging
- **Enhanced Logging**: Color-coded output with timing information
- **Comprehensive Error Handling**: PTR failures don't impact A record operations

## 2. Prerequisites

- Access to the appropriate ADMT server for the domain you want to manage
- Appropriate permissions to run PowerShell scripts and manage DNS records
- Basic knowledge of PowerShell commands
- The `set-dns.ps1` script file

## 3. Script Location

The script should be placed in the following location on the ADMT server:

```
C:\ansscripts\set-dns.ps1
```

If the directory doesn't exist, create it:

```powershell
New-Item -Path "C:\ansscripts" -ItemType Directory -Force
```

## 4. Script Parameters

The enhanced script accepts the following parameters:

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| Action | String | No | verify | The action to perform: verify, add, or remove |
| Domain | String | Yes | - | The domain for DNS operations (e.g., healthgrp.com.sg) |
| HostName | String | Yes | - | The hostname for the DNS record (without domain) |
| IpAddress | String | Only for 'add' | - | The IP address for the DNS record |
| TTL | Integer | No | 3600 | Time-to-live value in seconds |
| ManagePtr | Boolean | No | true | Enable/disable intelligent PTR record management |

## 4.1 PTR Management Parameter

The `ManagePtr` parameter controls intelligent PTR operations:

- **$true** (default): Enables intelligent PTR zone detection and management
- **$false**: Disables PTR operations, works with A records only (legacy mode)

## 5. ADMT Server Selection

Use the appropriate ADMT server based on the domain you want to manage:

| Domain | ADMT Server |
|--------|-------------|
| devhealthgrp.com.sg | HISADMTVDSEC01.devhealthgrp.com.sg |
| healthgrpexts.com.sg | HISADMTVSSEC01.healthgrpexts.com.sg |
| nnstg.local | HISADMTVSSEC02.nnstg.local |
| ses.shsu.com.sg | SHSADMTVDSEC02.ses.shsu.com.sg |
| shses.shs.com.sg | SHSADMTVPSEC12.shses.shs.com.sg |
| nhg.local | HISADMTVPSEC11.nhg.local |
| aic.local | HISADMTVPSEC02.aic.local |
| iltc.healthgrp.com.sg | HISADMTVPSEC04.iltc.healthgrp.com.sg |
| healthgrp.com.sg | HISADMTVPSEC05.healthgrp.com.sg |
| hcloud.healthgrp.com.sg | HISADMTVPSEC06.hcloud.healthgrp.com.sg |
| healthgrpextp.com.sg | HISADMTVPSEC08.healthgrpextp.com.sg |

## 6. Usage Examples

### 6.1 Verifying a DNS Record with Intelligent PTR Detection

To check if a DNS record exists with intelligent PTR zone detection:

```powershell
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

To verify A record only (disable PTR checking):

```powershell
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01 -ManagePtr $false
```

### 6.2 Adding a DNS Record with Intelligent PTR Management

To add a new DNS A record with intelligent PTR zone detection:

```powershell
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ******** -TTL 7200
```

To add A record only (disable PTR creation):

```powershell
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ******** -ManagePtr $false
```

### 6.3 Removing a DNS Record with Safe PTR Removal

To remove a DNS A record and its matching PTR record safely:

```powershell
.\set-dns.ps1 -Action remove -Domain healthgrp.com.sg -HostName server01
```

To remove A record only (preserve PTR records):

```powershell
.\set-dns.ps1 -Action remove -Domain healthgrp.com.sg -HostName server01 -ManagePtr $false
```

## 7. Special Cases

### 7.1 SingHealth Domains

For SingHealth domains, special PTR handling is implemented:

- **ses.shsu.com.sg**: A records are managed on sedcvssys22h1.ses.shsu.com.sg, but PTR records are managed on shdcvsys22h1.shsu.com.sg
- **shses.shs.com.sg**: A records are managed on sesdcvpsys01.shses.shs.com.sg, but PTR records are managed on sesdcvpsys11.shs.com.sg

Example for SingHealth staging:

```powershell
.\set-dns.ps1 -Action add -Domain ses.shsu.com.sg -HostName webserver -IpAddress ***********
```

Example for SingHealth production:

```powershell
.\set-dns.ps1 -Action add -Domain shses.shs.com.sg -HostName webserver -IpAddress ***********
```

## 8. Understanding the Enhanced Output

The enhanced script outputs information in two formats:

1. **Enhanced Text output**: Color-coded detailed information with timing data
2. **Enhanced JSON output**: Structured representation with PTR management details

### 8.1 Enhanced Text Output Features

- **Color-coded messages**: Different colors for different operation types
- **Timing information**: Zone detection timing in milliseconds
- **Detailed PTR operations**: Step-by-step PTR zone detection and operations
- **Clear status indicators**: Success, warning, and error messages

### 8.2 Enhanced JSON Output

Example enhanced JSON output:

```json
{
  "action": "add",
  "status": "A Record Added",
  "domain": "healthgrp.com.sg",
  "dns_server": "hisaddcvputl07.healthgrp.com.sg",
  "ptr_dns_server": "hisaddcvputl07.healthgrp.com.sg",
  "hostname": "server01",
  "ip_address": "********",
  "ttl": "7200",
  "ptr_record": "server01.healthgrp.com.sg",
  "ptr_management_enabled": true,
  "ptr_zone_detected": "2.1.10.in-addr.arpa",
  "ptr_operation_status": "Zone detected: 2-octet - PTR record created successfully",
  "errors": ""
}
```

### 8.3 New JSON Fields

- **ptr_management_enabled**: Shows if PTR management was active
- **ptr_zone_detected**: Shows which PTR zone was detected and used
- **ptr_operation_status**: Detailed status of PTR operations

## 9. Troubleshooting

### 9.1 Common Issues

#### Script Execution Policy

If you encounter execution policy restrictions:

```powershell
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
```

#### DNS Server Connection Issues

If the script cannot connect to the DNS server:

1. Verify network connectivity to the DNS server
2. Check if the DNS server name is correct in the script
3. Verify that the DNS server is running
4. Check if you have appropriate permissions

#### Authentication Issues

If you encounter authentication issues:

1. Ensure you're running the script with an account that has appropriate permissions
2. Try running the script as an administrator

### 9.2 Debugging

To get more detailed output, add the `-Verbose` parameter to the PowerShell commands:

```powershell
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01 -Verbose
```

## 10. Enhanced Testing Scenarios

### 10.1 PTR Zone Detection Testing

Test the intelligent PTR zone detection:

```powershell
# Test with different IP ranges to see zone detection
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName test1 -IpAddress ********
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName test2 -IpAddress ***********
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName test3 -IpAddress *************
```

### 10.2 PTR Management Testing

Test PTR management enabled vs disabled:

```powershell
# Add with PTR management enabled (default)
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Add with PTR management disabled
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server02 -IpAddress ******** -ManagePtr $false

# Verify both records
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server02 -ManagePtr $false
```

### 10.3 Safe PTR Removal Testing

Test safe PTR removal functionality:

```powershell
# Add a record with PTR
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Remove with PTR management (should remove matching PTR)
.\set-dns.ps1 -Action remove -Domain healthgrp.com.sg -HostName server01

# Verify removal
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

### 10.4 Idempotency Testing

Test that running the same operation multiple times produces the expected result:

```powershell
# Run the add operation twice with PTR management
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Verify the record
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

### 10.5 Update Testing

Test updating an existing record with a new IP address:

```powershell
# Add a record with one IP
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Update the record with a different IP
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress ********

# Verify the record has the new IP
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

### 10.6 Error Handling Testing

Test error handling scenarios:

```powershell
# Test with invalid IP address
.\set-dns.ps1 -Action add -Domain healthgrp.com.sg -HostName server01 -IpAddress 999.999.999.999

# Test with non-existent domain
.\set-dns.ps1 -Action verify -Domain nonexistent.domain -HostName server01

# Test remove operation on non-existent record
.\set-dns.ps1 -Action remove -Domain healthgrp.com.sg -HostName nonexistent
```

## 11. Enhanced Best Practices

1. **Always verify first**: Before adding or removing records, use the verify action to check the current state
2. **Use descriptive hostnames**: Follow naming conventions
3. **Document changes**: Keep a log of all changes made directly on the servers
4. **Test in staging first**: Test changes in development/staging environments before applying to production
5. **Monitor PTR zone detection**: Pay attention to which PTR zones are detected and used
6. **Use appropriate TTL values**: Use shorter TTL values for testing and longer values for production
7. **Test PTR management settings**: Verify behavior with both PTR management enabled and disabled
8. **Monitor timing information**: Use the timing data to identify potential DNS performance issues
9. **Validate PTR removal safety**: Ensure PTR records are only removed when they match the hostname
10. **Test error scenarios**: Regularly test error handling to ensure graceful degradation

## 12. Security Considerations

1. **Clean up after testing**: Remove the script from the server after testing
2. **Use least privilege accounts**: Only use accounts with the minimum required permissions
3. **Avoid hardcoding credentials**: Never include credentials in the script
4. **Audit script usage**: Keep track of who uses the script and when

## 13. Contact Information

For additional assistance, please contact:

- **Email**: <EMAIL>
- **Jira**: Create a Service Request ticket with the CES Operational Excellence Team
