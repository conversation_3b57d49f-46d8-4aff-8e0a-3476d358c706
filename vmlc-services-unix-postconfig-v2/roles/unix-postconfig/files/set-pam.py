#!/usr/bin/env python3
# Enhanced PAM accounts management script
# Version 2.0 - Improved error handling, validation, and security

import subprocess
import sys
import json
from datetime import datetime

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")

def validate_environment(environment):
    """Validate environment parameter"""
    valid_environments = ['staging', 'production', 'development']
    return environment.lower() in valid_environments

def validate_action(action):
    """Validate action parameter"""
    valid_actions = ['add', 'delete']
    return action.lower() in valid_actions

def user_exists(username):
    """Check if a user exists in the system"""
    try:
        result = subprocess.run(['id', username], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE, 
                              universal_newlines=True,
                              timeout=30)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        log_message(f"Timeout checking if user {username} exists", "ERROR")
        return False
    except Exception as e:
        log_message(f"Exception checking user {username}: {e}", "ERROR")
        return False

def group_exists(groupname):
    """Check if a group exists in the system"""
    try:
        result = subprocess.run(['getent', 'group', groupname], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE, 
                              universal_newlines=True,
                              timeout=30)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        log_message(f"Timeout checking if group {groupname} exists", "ERROR")
        return False
    except Exception as e:
        log_message(f"Exception checking group {groupname}: {e}", "ERROR")
        return False

def get_pamunix_accounts():
    """Get list of pamunix accounts and their min days settings"""
    try:
        command = "grep -E '^[^:]+[^!*]' /etc/shadow | cut -d: -f1,4 | grep 'pamunix'"
        result = subprocess.run(command, 
                              shell=True, 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE, 
                              universal_newlines=True,
                              timeout=60)
        
        if result.returncode == 0 and result.stdout.strip():
            accounts = []
            for line in result.stdout.strip().split('\n'):
                if ':' in line:
                    username, min_days = line.split(':', 1)
                    accounts.append({'username': username, 'min_days': min_days})
            return accounts
        else:
            return []
    except Exception as e:
        log_message(f"Exception getting pamunix accounts: {e}", "ERROR")
        return []

def set_pamunix_min_days():
    """Set the min days value to 0 for pamunix accounts"""
    try:
        accounts = get_pamunix_accounts()
        accounts_to_change = [acc['username'] for acc in accounts if acc['min_days'] == '1']
        
        if not accounts_to_change:
            log_message("All pamunix accounts already have min days set to 0")
            return True
        
        log_message(f"Changing min days to 0 for accounts: {', '.join(accounts_to_change)}")
        
        for username in accounts_to_change:
            result = subprocess.run(['chage', '-m', '0', username], 
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  universal_newlines=True,
                                  timeout=30)
            
            if result.returncode != 0:
                log_message(f"Failed to change min days for {username}: {result.stderr}", "ERROR")
                return False
            else:
                log_message(f"Successfully changed min days for {username}")
        
        return True
        
    except Exception as e:
        log_message(f"Exception setting pamunix min days: {e}", "ERROR")
        return False

def load_pam_configuration():
    """Load PAM user configuration"""
    return {
        'staging': {
            'ihisadm': ['pamunixadm2001', 'pamunixadm2002', 'pamunixadm2003', 'pamunixadm2004', 'pamunixadm2005', 'pamunixadm2006'],
            'pamunixread': ['pamunixread2001'],
            'pamunixdeadm': ['pamunixadm2011', 'pamunixadm2012'],
            'pamunixderead': ['pamunixread2011', 'pamunixread2012']
        },
        'production': {
            'ihisadm': ['pamunixadm1001', 'pamunixadm1002', 'pamunixadm1003', 'pamunixadm1004', 'pamunixadm1005', 'pamunixadm1006'],
            'pamunixread': ['pamunixread1001'],
            'pamunixdeadm': ['pamunixadm1011', 'pamunixadm1012'],
            'pamunixderead': ['pamunixread1011', 'pamunixread1012']
        },
        'development': {
            'ihisadm': ['pamunixadm3001', 'pamunixadm3002'],
            'pamunixread': ['pamunixread3001'],
            'pamunixdeadm': ['pamunixadm3011'],
            'pamunixderead': ['pamunixread3011']
        }
    }

def create_group(groupname):
    """Create a group if it doesn't exist"""
    try:
        if group_exists(groupname):
            log_message(f"Group {groupname} already exists")
            return True
        
        result = subprocess.run(['groupadd', '-f', groupname], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=30)
        
        if result.returncode == 0:
            log_message(f"Successfully created group {groupname}")
            return True
        else:
            log_message(f"Failed to create group {groupname}: {result.stderr}", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"Exception creating group {groupname}: {e}", "ERROR")
        return False

def create_user(username, groupname):
    """Create a user with specified primary group"""
    try:
        if user_exists(username):
            log_message(f"User {username} already exists")
            return True
        
        result = subprocess.run(['useradd', '-m', '-g', groupname, username], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=60)
        
        if result.returncode == 0:
            log_message(f"Successfully created user {username} with primary group {groupname}")
            return True
        else:
            log_message(f"Failed to create user {username}: {result.stderr}", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"Exception creating user {username}: {e}", "ERROR")
        return False

def delete_user(username):
    """Delete a user and their home directory"""
    try:
        if not user_exists(username):
            log_message(f"User {username} does not exist")
            return True
        
        result = subprocess.run(['userdel', '-r', username], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=60)
        
        if result.returncode == 0:
            log_message(f"Successfully deleted user {username}")
            return True
        else:
            log_message(f"Failed to delete user {username}: {result.stderr}", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"Exception deleting user {username}: {e}", "ERROR")
        return False

def manage_groups_and_users(environment, action):
    """Main function to manage groups and users"""
    try:
        pam_config = load_pam_configuration()
        
        if environment not in pam_config:
            log_message(f"Environment '{environment}' is not supported", "ERROR")
            return False

        success = True
        
        for group, users in pam_config[environment].items():
            if action == 'add':
                # Create group first
                if not create_group(group):
                    success = False
                    continue
                
                # Create users
                for user in users:
                    if not create_user(user, group):
                        success = False
                        
            elif action == 'delete':
                # Delete users first
                for user in users:
                    if not delete_user(user):
                        success = False
                
                # Skip deletion of standard groups
                protected_groups = ['ihisadm', 'pamunixread', 'pamunixdeadm', 'pamunixderead']
                if group not in protected_groups:
                    if group_exists(group):
                        try:
                            result = subprocess.run(['groupdel', group], 
                                                  stdout=subprocess.PIPE,
                                                  stderr=subprocess.PIPE,
                                                  universal_newlines=True,
                                                  timeout=30)
                            if result.returncode == 0:
                                log_message(f"Successfully deleted group {group}")
                            else:
                                log_message(f"Failed to delete group {group}: {result.stderr}", "ERROR")
                                success = False
                        except Exception as e:
                            log_message(f"Exception deleting group {group}: {e}", "ERROR")
                            success = False
                else:
                    log_message(f"Skipping deletion of protected group {group}")

        # Handle pamunix account min days setting
        if action == 'add':
            if not set_pamunix_min_days():
                success = False

        return success
        
    except Exception as e:
        log_message(f"Exception in manage_groups_and_users: {e}", "ERROR")
        return False

def main():
    """Main function with enhanced error handling"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        log_message("Usage: python set-pam.py <environment> [action]", "ERROR")
        log_message("Environment: staging, production, development", "ERROR")
        log_message("Action: add (default), delete", "ERROR")
        sys.exit(1)

    environment = sys.argv[1].strip().lower()
    action = sys.argv[2].strip().lower() if len(sys.argv) == 3 else 'add'

    # Validate inputs
    if not validate_environment(environment):
        log_message(f"Invalid environment: {environment}. Must be staging, production, or development", "ERROR")
        sys.exit(1)

    if not validate_action(action):
        log_message(f"Invalid action: {action}. Must be add or delete", "ERROR")
        sys.exit(1)

    log_message(f"Starting PAM accounts management: environment={environment}, action={action}")

    # Execute management
    success = manage_groups_and_users(environment, action)

    if success:
        log_message("PAM accounts management completed successfully")
        sys.exit(0)
    else:
        log_message("PAM accounts management completed with errors", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
