#!/usr/bin/env python3

import subprocess
import argparse

def user_exists(username):
    """Check if a user exists in the system."""
    result = subprocess.run(['id', username], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
    return result.returncode == 0

def get_pamunix_min_days():
    """Get the min days value for pamunix accounts."""
    command = "sudo grep -E '^[^:]+[^!*]' /etc/shadow | cut -d: -f1,4 | grep 'pamunix' | awk -F: '{print $1\":\"$2}'"
    result = subprocess.run(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
    return result.stdout.strip().split("\n")

def set_pamunix_min_days():
    """Set the min days value to 0 for pamunix accounts."""
    command = "sudo grep -E '^[^:]+[^!*]' /etc/shadow | cut -d: -f1,4 | grep 'pamunix' | awk -F: '{print $1}' | xargs -I{} sudo chage -m 0 {}"
    subprocess.run(command, shell=True, check=True)

def manage_groups_and_users(environment, action):
    groups_and_users = {
        'staging': {
            'ihisadm': ['pamunixadm2001', 'pamunixadm2002', 'pamunixadm2003', 'pamunixadm2004', 'pamunixadm2005', 'pamunixadm2006'],
            'pamunixread': ['pamunixread2001'],
            'pamunixdeadm': ['pamunixadm2011', 'pamunixadm2012'],
            'pamunixderead': ['pamunixread2011', 'pamunixread2012']
        },
        'production': {
            'ihisadm': ['pamunixadm1001', 'pamunixadm1002', 'pamunixadm1003', 'pamunixadm1004', 'pamunixadm1005', 'pamunixadm1006'],
            'pamunixread': ['pamunixread1001'],
            'pamunixdeadm': ['pamunixadm1011', 'pamunixadm1012'],
            'pamunixderead': ['pamunixread1011', 'pamunixread1012']
        }
    }

    environment = environment.lower()
    action = action.lower()

    if environment not in groups_and_users:
        print(f"Environment '{environment}' is not supported.")
        return

    for group, users in groups_and_users[environment].items():
        if action == 'add':
            # Create group if it doesn't exist
            subprocess.run(['sudo', 'groupadd', '-f', group], check=True)
            for user in users:
                # Check if user already exists before creating
                if not user_exists(user):
                    result = subprocess.run(['sudo', 'useradd', '-m', '-g', group, user], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
                    if result.returncode == 0:
                        print(f"Created user '{user}' with primary group '{group}'.")
                    else:
                        print(f"Failed to create user '{user}': {result.stderr}")
                else:
                    print(f"User '{user}' already exists.")

        elif action == 'delete':
            for user in users:
                # Check if user exists before trying to delete
                if user_exists(user):
                    result = subprocess.run(['sudo', 'userdel', '-r', user], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
                    if result.returncode == 0:
                        print(f"Deleted user '{user}'.")
                    else:
                        print(f"Failed to delete user '{user}': {result.stderr}")
                else:
                    print(f"User '{user}' does not exist.")
            # Check if group should be skipped for deletion
            if group not in ['ihisadm', 'pamunixread', 'pamunixdeadm', 'pamunixderead']:
                # Check if group exists before trying to delete
                result = subprocess.run(['getent', 'group', group], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
                if result.returncode == 0:
                    result = subprocess.run(['sudo', 'groupdel', group], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
                    if result.returncode == 0:
                        print(f"Deleted group '{group}'.")
                    else:
                        print(f"Failed to delete group '{group}': {result.stderr}")
                else:
                    print(f"Group '{group}' does not exist.")
            else:
                print(f"Skipping deletion of group '{group}'.")

    # Check and change pamunix accounts from 1 to 0
    pamunix_accounts = get_pamunix_min_days()
    accounts_to_change = [acc.split(":")[0] for acc in pamunix_accounts if acc.split(":")[1] == '1']

    if not accounts_to_change:
        print("All pamunix accounts already have min days set to 0.")
    else:
        print("Changing pamunix accounts from 1 to 0...")
        set_pamunix_min_days()
        print("Changed pamunix accounts from 1 to 0.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Manage user accounts and groups based on environment and action.')
    parser.add_argument('environment', type=str, help='The environment for which to manage user accounts and groups.')
    parser.add_argument('action', type=str, choices=['add', 'delete'], help='The action to perform: add or delete user accounts and groups.', default='add', nargs='?')

    args = parser.parse_args()
    manage_groups_and_users(args.environment.lower(), args.action.lower())
