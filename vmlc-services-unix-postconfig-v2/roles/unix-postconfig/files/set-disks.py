#!/usr/bin/env python3
# Enhanced disk management and filesystem configuration script
# Version 2.0 - Improved error handling, validation, and security

import subprocess
import json
import os
import sys
import socket
import argparse
import re
from datetime import datetime

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")

def run_command(command, timeout=60):
    """Run a shell command safely with timeout and error handling"""
    try:
        log_message(f"Executing command: {command}", "DEBUG")
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            timeout=timeout
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        log_message(f"Command failed: {command}", "ERROR")
        log_message(f"Error output: {e.stderr}", "ERROR")
        return ""
    except subprocess.TimeoutExpired:
        log_message(f"Command timed out: {command}", "ERROR")
        return ""
    except Exception as e:
        log_message(f"Exception running command: {e}", "ERROR")
        return ""

def validate_mount_point(mount_point):
    """Validate mount point format and security"""
    if not mount_point:
        return False

    # Check for valid absolute path
    if not mount_point.startswith('/'):
        log_message(f"Mount point must be absolute path: {mount_point}", "ERROR")
        return False

    # Check for dangerous paths
    dangerous_paths = ['/etc', '/bin', '/sbin', '/usr', '/var', '/boot', '/sys', '/proc', '/dev']
    if any(mount_point.startswith(path) for path in dangerous_paths):
        log_message(f"Mount point in dangerous location: {mount_point}", "ERROR")
        return False

    # Check for valid characters
    if not re.match(r'^[a-zA-Z0-9/_-]+$', mount_point):
        log_message(f"Mount point contains invalid characters: {mount_point}", "ERROR")
        return False

    return True

def validate_disk_name(disk_name):
    """Validate disk name format"""
    if not disk_name:
        return False

    # Check for valid disk name pattern
    if not re.match(r'^[a-z]+[0-9]*$', disk_name):
        log_message(f"Invalid disk name format: {disk_name}", "ERROR")
        return False

    return True

def validate_payload(payload):
    """Validate the input payload structure"""
    if not isinstance(payload, list):
        log_message("Payload must be a list", "ERROR")
        return False

    for i, item in enumerate(payload):
        if not isinstance(item, dict):
            log_message(f"Payload item {i} must be a dictionary", "ERROR")
            return False

        if "Data Drive Name" not in item:
            log_message(f"Payload item {i} missing 'Data Drive Name'", "ERROR")
            return False

        mount_point = item["Data Drive Name"]
        if not validate_mount_point(mount_point):
            return False

    return True

def get_all_disks():
    """Get all disks of type 'disk'"""
    try:
        output = run_command("lsblk -dn -o NAME,TYPE | grep 'disk' | awk '{print $1}'")
        if output:
            disks = output.split()
            log_message(f"Found disks: {disks}")
            return disks
        return []
    except Exception as e:
        log_message(f"Error getting all disks: {e}", "ERROR")
        return []

def get_all_partitions():
    """Get all partitions"""
    try:
        output = run_command("lsblk -dn -o NAME,TYPE | grep 'part' | awk '{print $1}'")
        if output:
            partitions = output.split()
            log_message(f"Found partitions: {partitions}")
            return partitions
        return []
    except Exception as e:
        log_message(f"Error getting all partitions: {e}", "ERROR")
        return []

def get_mounted_disks_and_partitions():
    """Get all mounted disks and partitions"""
    try:
        output = run_command("lsblk -ln -o NAME,MOUNTPOINT | awk '$2!=\"\" {print $1}'")
        if output:
            mounted = output.split()
            log_message(f"Found mounted devices: {mounted}")
            return mounted
        return []
    except Exception as e:
        log_message(f"Error getting mounted devices: {e}", "ERROR")
        return []

def is_disk_partitioned(disk):
    """Check if a disk has existing partitions"""
    try:
        if not validate_disk_name(disk):
            return False

        output = run_command(f"lsblk -dn -o NAME,TYPE /dev/{disk} | grep 'part'")
        partitioned = len(output.split()) > 0
        log_message(f"Disk {disk} partitioned: {partitioned}")
        return partitioned
    except Exception as e:
        log_message(f"Error checking if disk {disk} is partitioned: {e}", "ERROR")
        return False

def is_disk_mounted(disk):
    """Check if a disk is mounted"""
    try:
        if not validate_disk_name(disk):
            return False

        output = run_command(f"lsblk -ln -o NAME,MOUNTPOINT /dev/{disk} | awk '$2!=\"\" {{print $2}}'")
        mounted = len(output.split()) > 0
        log_message(f"Disk {disk} mounted: {mounted}")
        return mounted
    except Exception as e:
        log_message(f"Error checking if disk {disk} is mounted: {e}", "ERROR")
        return False

def get_primary_disks():
    """Identify primary disks based on partitions and mount points"""
    try:
        primary_disks = set()
        all_disks = get_all_disks()
        all_partitions = get_all_partitions()

        for disk in all_disks:
            if any(part.startswith(disk) for part in all_partitions) or is_disk_mounted(disk):
                primary_disks.add(disk)

        log_message(f"Primary disks identified: {primary_disks}")
        return primary_disks
    except Exception as e:
        log_message(f"Error identifying primary disks: {e}", "ERROR")
        return set()

def get_unmounted_disks():
    """Identify unmounted disks with no existing partitions or filesystems"""
    try:
        all_disks = get_all_disks()
        primary_disks = get_primary_disks()

        unmounted_disks = []
        for disk in all_disks:
            if disk not in primary_disks:
                unmounted_disks.append(f"/dev/{disk}")

        log_message(f"Unmounted disks found: {unmounted_disks}")
        return unmounted_disks
    except Exception as e:
        log_message(f"Error getting unmounted disks: {e}", "ERROR")
        return []

def backup_fstab():
    """Create backup of /etc/fstab"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"/etc/fstab.backup.{timestamp}"

        run_command(f"cp /etc/fstab {backup_path}")
        log_message(f"Created fstab backup: {backup_path}")
        return backup_path
    except Exception as e:
        log_message(f"Failed to backup fstab: {e}", "ERROR")
        return None

def format_disk(disk):
    """Format the disk to create a new partition"""
    try:
        if not disk.startswith('/dev/'):
            log_message(f"Invalid disk path: {disk}", "ERROR")
            return False

        disk_name = disk.split('/')[-1]
        if not validate_disk_name(disk_name):
            return False

        log_message(f"Formatting disk {disk}")

        # Use parted for more reliable partitioning
        parted_command = f"parted -s {disk} mklabel gpt mkpart primary 0% 100%"
        output = run_command(parted_command)

        if output is not None:
            log_message(f"Successfully formatted disk {disk}")
            return True
        else:
            log_message(f"Failed to format disk {disk}", "ERROR")
            return False

    except Exception as e:
        log_message(f"Exception formatting disk {disk}: {e}", "ERROR")
        return False

def create_mount_point(mount_point):
    """Create a new mount point directory"""
    try:
        if not validate_mount_point(mount_point):
            return False

        if not os.path.exists(mount_point):
            os.makedirs(mount_point, mode=0o755, exist_ok=True)
            log_message(f"Created mount point: {mount_point}")
            return True
        else:
            log_message(f"Mount point already exists: {mount_point}")
            return True

    except Exception as e:
        log_message(f"Failed to create mount point {mount_point}: {e}", "ERROR")
        return False

def create_partition(disk, mount_point, lv_name, vg_name):
    """Create a new partition and logical volume"""
    try:
        if not validate_mount_point(mount_point):
            return False

        disk_partition = f"{disk}1"

        # Check if physical volume already exists
        pv_check = run_command(f"pvs | grep -q {disk_partition} && echo 'exists' || echo 'not_exists'")

        if pv_check == "not_exists":
            # Create physical volume
            pv_command = f"pvcreate {disk_partition}"
            if not run_command(pv_command):
                log_message(f"Failed to create physical volume on {disk_partition}", "ERROR")
                return False

            # Create volume group
            vg_command = f"vgcreate {vg_name} {disk_partition}"
            if not run_command(vg_command):
                log_message(f"Failed to create volume group {vg_name}", "ERROR")
                return False

            # Create logical volume
            lv_command = f"lvcreate -l 100%FREE -n {lv_name} {vg_name}"
            if not run_command(lv_command):
                log_message(f"Failed to create logical volume {lv_name}", "ERROR")
                return False

            # Format with XFS
            format_command = f"mkfs.xfs /dev/{vg_name}/{lv_name}"
            if not run_command(format_command):
                log_message(f"Failed to format logical volume with XFS", "ERROR")
                return False

            # Mount the filesystem
            mount_command = f"mount /dev/{vg_name}/{lv_name} {mount_point}"
            if not run_command(mount_command):
                log_message(f"Failed to mount filesystem at {mount_point}", "ERROR")
                return False

            log_message(f"Successfully created and mounted logical volume {lv_name} at {mount_point}")
            return True
        else:
            log_message(f"Physical volume on {disk_partition} already exists")
            return True

    except Exception as e:
        log_message(f"Exception creating partition: {e}", "ERROR")
        return False

def add_to_fstab(vg_name, lv_name, mount_point):
    """Add the new logical volume to /etc/fstab"""
    try:
        if not validate_mount_point(mount_point):
            return False

        fstab_entry = f"/dev/{vg_name}/{lv_name} {mount_point} xfs defaults 0 0"

        # Check if entry already exists
        with open('/etc/fstab', 'r') as fstab:
            content = fstab.read()
            if fstab_entry in content:
                log_message(f"Entry for {mount_point} already exists in /etc/fstab")
                return True

        # Backup fstab before modification
        backup_fstab()

        # Add new entry
        with open('/etc/fstab', 'a') as fstab:
            fstab.write(f"\n{fstab_entry}\n")

        log_message(f"Added {mount_point} to /etc/fstab")
        return True

    except Exception as e:
        log_message(f"Failed to add entry to fstab: {e}", "ERROR")
        return False

def determine_lv_vg_names(hostname, disk_name):
    """Determine LV and VG names based on hostname"""
    try:
        # Sanitize hostname and disk name
        clean_hostname = re.sub(r'[^a-zA-Z0-9]', '', hostname.split('.')[0])
        clean_disk = re.sub(r'[^a-zA-Z0-9]', '', disk_name)

        lv_name = f"{clean_hostname}_{clean_disk}_lv"
        vg_name = f"{clean_hostname}_{clean_disk}_vg"

        log_message(f"Generated LV name: {lv_name}, VG name: {vg_name}")
        return lv_name, vg_name

    except Exception as e:
        log_message(f"Error determining LV/VG names: {e}", "ERROR")
        return None, None

def configure_new_filesystems(payload, hostname):
    """Configure new filesystems based on the payload"""
    try:
        if not validate_payload(payload):
            log_message("Invalid payload provided", "ERROR")
            return False

        new_disks = get_unmounted_disks()
        if len(new_disks) < len(payload):
            log_message(f"Not enough unmounted disks available. Required: {len(payload)}, Available: {len(new_disks)}", "ERROR")
            return False

        log_message(f"Configuring {len(payload)} filesystems on {len(new_disks)} available disks")

        success_count = 0
        for i, disk in enumerate(new_disks[:len(payload)]):
            try:
                data_drive = payload[i]
                mount_point = data_drive["Data Drive Name"]
                disk_name = disk.split('/')[-1]

                log_message(f"Processing disk {disk} for mount point {mount_point}")

                lv_name, vg_name = determine_lv_vg_names(hostname, disk_name)
                if not lv_name or not vg_name:
                    log_message(f"Failed to determine LV/VG names for disk {disk}", "ERROR")
                    continue

                # Format the disk if not already partitioned
                if not is_disk_partitioned(disk_name):
                    log_message(f"Formatting disk {disk}")
                    if not format_disk(disk):
                        log_message(f"Failed to format disk {disk}", "ERROR")
                        continue
                else:
                    log_message(f"Disk {disk} is already partitioned")

                # Create the mount point if it does not exist
                log_message(f"Creating mount point {mount_point}")
                if not create_mount_point(mount_point):
                    log_message(f"Failed to create mount point {mount_point}", "ERROR")
                    continue

                # Create the partition and logical volume
                log_message(f"Creating partition on {disk} and logical volume {lv_name} at {mount_point}")
                if not create_partition(disk, mount_point, lv_name, vg_name):
                    log_message(f"Failed to create partition/LV for disk {disk}", "ERROR")
                    continue

                # Add to /etc/fstab
                if not add_to_fstab(vg_name, lv_name, mount_point):
                    log_message(f"Failed to add {mount_point} to fstab", "ERROR")
                    continue

                success_count += 1
                log_message(f"Successfully configured disk {disk} with mount point {mount_point}")

            except Exception as e:
                log_message(f"Exception configuring disk {disk}: {e}", "ERROR")
                continue

        log_message(f"Successfully configured {success_count} out of {len(payload)} filesystems")
        return success_count == len(payload)

    except Exception as e:
        log_message(f"Exception in configure_new_filesystems: {e}", "ERROR")
        return False

def get_disk_info():
    """Get information about all disks"""
    try:
        new_disks = get_unmounted_disks()
        disks_info = []

        for disk in new_disks:
            disk_name = disk.split('/')[-1]

            disk_status = {
                "disk": disk,
                "partitioned": is_disk_partitioned(disk_name),
                "mounted": is_disk_mounted(disk_name),
                "actions": []
            }

            if not disk_status["partitioned"] and not disk_status["mounted"]:
                disk_status["actions"].append("unmounted and no existing partitions")
            elif disk_status["partitioned"]:
                disk_status["actions"].append("disk has partitions")
            elif disk_status["mounted"]:
                disk_status["actions"].append("disk is mounted")

            disks_info.append(disk_status)

        return disks_info

    except Exception as e:
        log_message(f"Exception getting disk info: {e}", "ERROR")
        return []

def verify_filesystem_configuration(payload, hostname):
    """Verify that filesystems were configured correctly"""
    try:
        log_message("Verifying filesystem configuration...")

        for item in payload:
            mount_point = item["Data Drive Name"]

            # Check if mount point exists and is mounted
            if not os.path.exists(mount_point):
                log_message(f"Mount point {mount_point} does not exist", "ERROR")
                return False

            # Check if it's actually mounted
            mount_check = run_command(f"mountpoint -q {mount_point} && echo 'mounted' || echo 'not_mounted'")
            if mount_check != "mounted":
                log_message(f"Mount point {mount_point} is not mounted", "ERROR")
                return False

            # Check fstab entry
            with open('/etc/fstab', 'r') as fstab:
                content = fstab.read()
                if mount_point not in content:
                    log_message(f"Mount point {mount_point} not found in /etc/fstab", "ERROR")
                    return False

            log_message(f"Filesystem {mount_point} verified successfully")

        log_message("All filesystems verified successfully")
        return True

    except Exception as e:
        log_message(f"Exception verifying filesystem configuration: {e}", "ERROR")
        return False

def main():
    """Main function with enhanced error handling"""
    parser = argparse.ArgumentParser(description='Enhanced Disk Management Script v2.0')
    parser.add_argument('--payload', type=str, help='JSON string of the payload for filesystem configuration')
    parser.add_argument('--list-disks', action='store_true', help='List available unmounted disks')
    parser.add_argument('--verify', action='store_true', help='Verify existing filesystem configuration')

    args = parser.parse_args()

    try:
        hostname = socket.gethostname().split('.')[0]  # Use short hostname
        log_message(f"Starting disk management for hostname: {hostname}")

        if args.list_disks:
            # List available disks
            disks_info = get_disk_info()
            if disks_info:
                print(json.dumps(disks_info, indent=4))
                log_message(f"Found {len(disks_info)} unmounted disks")
            else:
                print(json.dumps({"message": "No new unmounted disks found."}, indent=4))
                log_message("No unmounted disks found")
            return

        if args.payload:
            # Configure filesystems
            try:
                payload = json.loads(args.payload)
                log_message(f"Processing payload with {len(payload)} filesystem requests")

                if configure_new_filesystems(payload, hostname):
                    # Always verify after configuration
                    if verify_filesystem_configuration(payload, hostname):
                        log_message("Disk configuration completed and verified successfully")
                        sys.exit(0)
                    else:
                        log_message("Disk configuration verification failed", "ERROR")
                        sys.exit(1)
                else:
                    log_message("Disk configuration failed", "ERROR")
                    sys.exit(1)

            except json.JSONDecodeError as e:
                log_message(f"Invalid JSON payload: {e}", "ERROR")
                sys.exit(1)
        else:
            # Default behavior - list disks
            disks_info = get_disk_info()
            if disks_info:
                print(json.dumps(disks_info, indent=4))
            else:
                print(json.dumps({"message": "No new unmounted disks found."}, indent=4))

    except Exception as e:
        log_message(f"Unexpected error in main: {e}", "ERROR")
        sys.exit(1)

if __name__ == "__main__":
    main()
