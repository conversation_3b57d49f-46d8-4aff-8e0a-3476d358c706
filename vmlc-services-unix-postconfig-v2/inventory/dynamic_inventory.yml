# Minimal Inventory for Dynamic Host Addition
# Use this inventory when all hosts will be added dynamically
# Groups are pre-defined to support dynamic host addition

all:
  children:
    production:
      children:
        hdc1_production:
          hosts: {}
          vars:
            dc_location: HDC1
            Environment: production
            
        hdc2_production:
          hosts: {}
          vars:
            dc_location: HDC2
            Environment: production
            
    staging:
      children:
        hdc1_staging:
          hosts: {}
          vars:
            dc_location: HDC1
            Environment: staging
            
        hdc2_staging:
          hosts: {}
          vars:
            dc_location: HDC2
            Environment: staging
            
    development:
      children:
        hdc1_development:
          hosts: {}
          vars:
            dc_location: HDC1
            Environment: development
            
        hdc2_development:
          hosts: {}
          vars:
            dc_location: HDC2
            Environment: development

  vars:
    # Global variables for all dynamically added hosts
    ansible_user: ansible
    ansible_ssh_private_key_file: ~/.ssh/id_rsa
    ansible_python_interpreter: /usr/bin/python3
    
    # Default configuration values
    pam_action: add
    debug_output: false
    ignore_errors: false
    
    # Timeout settings
    ansible_ssh_timeout: 30
    ansible_command_timeout: 300
    
    # Dynamic host management
    cleanup_dynamic_hosts: false
