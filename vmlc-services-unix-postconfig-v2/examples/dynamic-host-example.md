# Dynamic Host Addition Example

This document demonstrates how to use the dynamic host addition functionality for newly provisioned servers.

## Scenario

You have just provisioned a new RHEL/Oracle Linux server with the following details:
- **FQDN**: `newprod01.hcloud.healthgrp.com.sg`
- **IP Address**: `*************`
- **Environment**: `production`
- **Data Center**: `HDC2`
- **Additional Requirements**: Create `/opt/app` and `/var/data` filesystems

The server is not yet in your Ansible inventory, but you need to configure it immediately.

## Solution: Dynamic Host Addition

### Step 1: Prepare Filesystem Configuration (Optional)

Create a filesystem configuration file if you need additional disks:

```bash
cat > /tmp/filesystem-config.json << EOF
[
  {
    "Data Drive Name": "/opt/app"
  },
  {
    "Data Drive Name": "/var/data"
  }
]
EOF
```

### Step 2: Execute Configuration with Dynamic Host Addition

#### Option A: Using ansible-playbook directly

```bash
ansible-playbook -i inventory/dynamic_inventory.yml main.yml -e '{
  "FQDN": "newprod01.hcloud.healthgrp.com.sg",
  "PRD_IP": "*************",
  "Environment": "production",
  "dc_location": "HDC2",
  "filesystem_payload": [
    {"Data Drive Name": "/opt/app"},
    {"Data Drive Name": "/var/data"}
  ],
  "debug_output": true
}'
```

#### Option B: Using the example script

```bash
./examples/run-postconfig.sh \
  -f newprod01.hcloud.healthgrp.com.sg \
  -i ************* \
  -e production \
  -l HDC2 \
  -F /tmp/filesystem-config.json \
  -D \
  -d
```

### Step 3: Monitor Execution

The playbook will:

1. **Validate Variables**: Check that all required parameters are provided
2. **Add Host Dynamically**: Add the new host to the `hdc2_production` group
3. **Test Connectivity**: Verify SSH connectivity to the new server
4. **Execute Configuration**: Run all configuration tasks on the target host
5. **Generate Summary**: Provide a completion summary with status

### Expected Output

```
PLAY [Dynamic Host Management and VM Post-Configuration] **********************

TASK [Validate required variables for dynamic host addition] ******************
ok: [localhost]

TASK [Add new host to inventory dynamically] **********************************
changed: [localhost]

TASK [Test connectivity to newly added host] **********************************
ok: [localhost]

PLAY [VM Post-Configuration Tasks for Dynamically Added Hosts] ***************

TASK [Gathering Facts] *********************************************************
ok: [newprod01.hcloud.healthgrp.com.sg]

TASK [unix-postconfig : Log task start] ***************************************
ok: [newprod01.hcloud.healthgrp.com.sg]

[... configuration tasks execute ...]

PLAY [Post-Configuration Summary and Cleanup] *********************************

TASK [Display configuration summary] ******************************************
ok: [localhost] => {
    "msg": [
        "=== VM Post-Configuration Summary ===",
        "Host: newprod01.hcloud.healthgrp.com.sg",
        "Status: completed",
        "Environment: production",
        "DC Location: HDC2",
        "Dynamically Added: true",
        "Completed: 2024-01-15T10:30:45Z",
        "==================================="
    ]
}
```

## What Happens Behind the Scenes

### 1. Dynamic Host Addition

The playbook uses the `add_host` module to add the new server:

```yaml
- name: Add new host to inventory dynamically
  ansible.builtin.add_host:
    name: "newprod01.hcloud.healthgrp.com.sg"
    groups: "hdc2_production"
    ansible_host: "*************"
    # ... additional variables
```

### 2. Group Assignment Logic

Based on the runtime variables:
- **Environment**: `production` → Added to `production` parent group
- **DC Location**: `HDC2` → Added to `hdc2_production` specific group
- **Combined**: Host becomes member of both `production` and `hdc2_production`

### 3. Variable Inheritance

All runtime variables are passed to the target host:
- `FQDN`, `PRD_IP`, `Environment`, `dc_location`
- `filesystem_payload`, `pam_action`, `debug_output`
- Connection parameters (`ansible_user`, `ansible_ssh_private_key_file`, etc.)

### 4. Configuration Execution

The second play targets the dynamically added host:
```yaml
- name: VM Post-Configuration Tasks for Dynamically Added Hosts
  hosts: "{{ hostvars['localhost']['var_fqdn'] }}"
```

## Verification

After execution, verify the configuration:

### 1. Check Hostname
```bash
ssh ansible@************* "hostnamectl"
```

### 2. Verify Filesystems
```bash
ssh ansible@************* "df -h | grep -E '(opt/app|var/data)'"
```

### 3. Check Services
```bash
ssh ansible@************* "systemctl status chronyd"
```

### 4. Review Logs
```bash
ssh ansible@************* "tail -20 /var/log/ansible-postconfig.log"
```

## Troubleshooting

### Common Issues

1. **SSH Connectivity Failure**
   - Verify the IP address is correct
   - Ensure SSH service is running on the target
   - Check firewall rules and network connectivity
   - Verify SSH key authentication is configured

2. **Variable Validation Errors**
   - Check FQDN format (must be valid hostname)
   - Verify IP address format
   - Ensure dc_location is HDC1 or HDC2
   - Confirm Environment is production/staging/development

3. **Permission Issues**
   - Verify the ansible user has sudo privileges
   - Check SSH key permissions (600 for private key)
   - Ensure target directories are writable

### Debug Mode

Enable debug output for detailed troubleshooting:
```bash
./examples/run-postconfig.sh \
  -f newprod01.hcloud.healthgrp.com.sg \
  -i ************* \
  -e production \
  -l HDC2 \
  -D \
  -d  # Enable debug mode
```

## Integration with AAP

This dynamic host functionality works seamlessly with Ansible Automation Platform:

1. **Job Templates**: Create job templates that accept runtime variables
2. **Surveys**: Use surveys to collect FQDN, IP, Environment, and Location
3. **Workflows**: Integrate with provisioning workflows
4. **Credentials**: Use AAP credential management for SSH keys
5. **Logging**: Leverage AAP's centralized logging and reporting

## Best Practices

1. **Use Dynamic Inventory**: Prefer `dynamic_inventory.yml` for new server scenarios
2. **Enable Logging**: Always enable comprehensive logging for audit trails
3. **Test Connectivity**: The built-in connectivity test prevents configuration failures
4. **Validate Variables**: Use the built-in validation to catch errors early
5. **Monitor Execution**: Review logs and summaries for successful completion
6. **Document Changes**: Maintain records of dynamically configured servers
