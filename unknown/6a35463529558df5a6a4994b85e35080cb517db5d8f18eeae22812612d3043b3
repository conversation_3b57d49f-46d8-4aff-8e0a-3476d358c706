# Disk management and filesystem configuration tasks
# Enhanced with proper error handling and validation

- name: Log disk configuration start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting disk configuration"
  delegate_to: localhost

- name: Validate disk payload if provided
  ansible.builtin.assert:
    that:
      - disk_payload is defined
      - disk_payload | length > 0
      - disk_payload is iterable
    fail_msg: "Disk payload must be provided and contain at least one filesystem configuration"
    success_msg: "Disk payload validated successfully"
  when: disk_payload is defined

- name: List available unmounted disks
  ansible.builtin.script: "{{ role_path }}/files/set-disks.py --list-disks"
  register: available_disks_result
  failed_when: false
  changed_when: false

- name: Parse available disks information
  ansible.builtin.set_fact:
    available_disks_info: "{{ available_disks_result.stdout | from_json }}"
  when: 
    - available_disks_result.rc == 0
    - available_disks_result.stdout != ""

- name: Log available disks
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Available unmounted disks: {{ available_disks_info | length if available_disks_info is defined else 0 }}"
  delegate_to: localhost

- name: Debug available disks output
  ansible.builtin.debug:
    var: available_disks_info
  when: 
    - debug_output | default(false)
    - available_disks_info is defined

- name: Check if disk configuration is needed
  ansible.builtin.set_fact:
    disk_configuration_needed: "{{ disk_payload is defined and disk_payload | length > 0 }}"

- name: Configure filesystems when payload is provided
  block:
    - name: Validate sufficient disks available
      ansible.builtin.assert:
        that:
          - available_disks_info is defined
          - available_disks_info | length >= (disk_payload | length)
        fail_msg: "Not enough unmounted disks available. Required: {{ disk_payload | length }}, Available: {{ available_disks_info | length if available_disks_info is defined else 0 }}"
        success_msg: "Sufficient unmounted disks available for configuration"

    - name: Install required packages for disk management
      ansible.builtin.package:
        name:
          - lvm2
          - xfsprogs
          - parted
        state: present
      register: package_install_result
      failed_when: false

    - name: Handle package installation failure
      ansible.builtin.fail:
        msg: "Failed to install required packages for disk management"
      when: 
        - package_install_result.rc != 0
        - not (ignore_configuration_errors | default(false))

    - name: Configure new filesystems
      ansible.builtin.script: "{{ role_path }}/files/set-disks.py --payload '{{ disk_payload | to_json }}'"
      register: disk_config_result
      failed_when: false

    - name: Handle disk configuration failure
      ansible.builtin.fail:
        msg: "Disk configuration failed: {{ disk_config_result.stderr | default('Unknown error') }}"
      when: 
        - disk_config_result.rc != 0
        - not (ignore_configuration_errors | default(false))

    - name: Log disk configuration result
      ansible.builtin.lineinfile:
        path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
        line: "{{ ansible_date_time.iso8601 }} - Disk configuration {{ 'completed' if disk_config_result.rc == 0 else 'failed' }}"
      delegate_to: localhost

    - name: Debug disk configuration output
      ansible.builtin.debug:
        var: disk_config_result.stdout_lines
      when: debug_output | default(false)

    - name: Verify filesystem mounts
      ansible.builtin.command: "mountpoint {{ item['Data Drive Name'] }}"
      register: mount_verification
      failed_when: false
      changed_when: false
      loop: "{{ disk_payload }}"
      when: disk_config_result.rc == 0

    - name: Log mount verification results
      ansible.builtin.lineinfile:
        path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
        line: "{{ ansible_date_time.iso8601 }} - Mount verification for {{ item.item['Data Drive Name'] }}: {{ 'success' if item.rc == 0 else 'failed' }}"
      delegate_to: localhost
      loop: "{{ mount_verification.results }}"
      when: 
        - mount_verification is defined
        - mount_verification.results is defined

    - name: Reload systemd daemon for new mounts
      ansible.builtin.systemd:
        daemon_reload: true
      when: disk_config_result.rc == 0

  when: disk_configuration_needed | default(false)

- name: Log disk configuration completion
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Disk configuration tasks completed"
  delegate_to: localhost
