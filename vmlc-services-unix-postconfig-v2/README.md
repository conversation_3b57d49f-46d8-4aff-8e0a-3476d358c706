# VM Lifecycle Services - Unix Post-Configuration v2

Enhanced Ansible automation for RHEL/Oracle Linux VM post-configuration with improved error handling, security, and AAP compatibility.

**Author:** CES Operational Excellence Team  
**Version:** 2.0  
**Date:** 2024

## Overview

This project provides enterprise-grade automation for configuring newly provisioned RHEL/Oracle Linux virtual machines. Version 2 includes significant improvements in Ansible best practices, error handling, security, and maintainability.

## Key Improvements in v2

### ✅ Ansible Best Practices & AAP Compatibility
- **FQCN Usage**: All modules use Fully Qualified Collection Names (`ansible.builtin.*`)
- **Variable Naming**: Reserved name conflicts resolved with `var_` prefix
- **Role-based Structure**: Modular, reusable role architecture
- **Proper Error Handling**: Replaced `ignore_errors` with structured error management

### ✅ Enhanced Security & Validation
- **Input Validation**: Comprehensive validation for all parameters
- **No Hardcoded Credentials**: Configuration moved to Ansible variables
- **Privilege Escalation**: Proper use of Ansible's `become` instead of sudo in scripts
- **Backup Creation**: Automatic backups before configuration changes

### ✅ Improved Logging & Monitoring
- **Structured Logging**: Centralized logging with timestamps and levels
- **Operation Tracking**: Detailed logging of all configuration steps
- **Verification Checks**: Post-configuration validation and testing
- **Debug Controls**: Configurable debug output

### ✅ Better Error Handling
- **Graceful Failures**: Proper error handling without breaking execution
- **Recovery Mechanisms**: Backup and rollback capabilities
- **Timeout Management**: Configurable timeouts for all operations
- **Validation Gates**: Pre-flight checks before configuration

## Project Structure

```
vmlc-services-unix-postconfig-v2/
├── main.yml                                    # Main playbook entry point
├── README.md                                   # This documentation
├── roles/
│   └── unix-postconfig/
│       ├── tasks/
│       │   ├── main.yml                        # Main task orchestration
│       │   ├── configure-hostname.yml          # Hostname and /etc/hosts
│       │   ├── configure-pam.yml               # PAM accounts management
│       │   ├── configure-netbackup.yml         # NetBackup bp.conf
│       │   ├── configure-chrony.yml            # NTP time synchronization
│       │   └── configure-dns.yml               # DNS resolution
│       ├── handlers/
│       │   └── main.yml                        # Event handlers (reboot, etc.)
│       ├── vars/
│       │   └── main.yml                        # Configuration variables
│       └── files/
│           ├── set-host.py                     # Enhanced hostname script
│           ├── set-pam.py                      # Enhanced PAM script
│           ├── set-bpconf.py                   # Enhanced NetBackup script
│           ├── set-chrony.py                   # Enhanced Chrony script
│           └── set-resolv.py                   # Enhanced DNS script
```

## Configuration Components

### 1. Hostname & /etc/hosts Configuration
- Sets system hostname using `hostnamectl`
- Updates `/etc/hosts` with proper entries
- Includes NetBackup, RedHat Capsule, and infrastructure servers
- Validates hostname format and IP addresses

### 2. PAM Accounts Management
- Creates environment-specific user accounts and groups
- Supports production, staging, and development environments
- Manages pamunix account password policies
- Includes user existence validation

### 3. NetBackup Configuration
- Configures `/usr/openv/netbackup/bp.conf`
- Includes all required backup servers for domains 2, 2B, and 2C
- Sets client name and connection options
- Validates configuration syntax

### 4. Time Synchronization (Chrony)
- Configures NTP servers based on data center location
- Supports HDC1 (AMK) and HDC2 (FORT) locations
- Maintains redundant NTP server configuration
- Tests configuration syntax

### 5. DNS Resolution
- Configures `/etc/resolv.conf` for domain-specific DNS
- Supports multiple domains (healthgrp.com.sg, hcloud.healthgrp.com.sg, etc.)
- Location-aware DNS server prioritization
- Includes DNS resolution testing

## Usage

### Basic Execution

```bash
ansible-playbook -i inventory main.yml -e '{
  "FQDN": "server01.hcloud.healthgrp.com.sg",
  "PRD_IP": "*************",
  "Environment": "production",
  "dc_location": "HDC2",
  "debug_output": false
}'
```

### Advanced Options

```bash
ansible-playbook -i inventory main.yml \
  --ask-vault-pass \
  -e FQDN="server01.hcloud.healthgrp.com.sg" \
  -e PRD_IP="*************" \
  -e Environment="production" \
  -e pam_action="add" \
  -e dc_location="HDC2" \
  -e debug_output=true \
  -e ignore_errors=false \
  --tags "hostname,pam,dns"
```

### Required Variables

| Variable | Description | Valid Values | Example |
|----------|-------------|--------------|---------|
| `FQDN` | Fully Qualified Domain Name | Valid FQDN | `server01.hcloud.healthgrp.com.sg` |
| `PRD_IP` | Production IP Address | Valid IPv4 | `*************` |
| `Environment` | Target Environment | `production`, `staging`, `development` | `production` |
| `dc_location` | Data Center Location | `HDC1`, `HDC2` | `HDC2` |

### Optional Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `pam_action` | PAM accounts action | `add` | `add`, `delete` |
| `debug_output` | Enable debug output | `false` | `true`, `false` |
| `ignore_errors` | Continue on errors | `false` | `true`, `false` |

## Tags

Use tags to run specific configuration components:

- `hostname` / `host`: Hostname and /etc/hosts configuration
- `pam` / `accounts`: PAM accounts management
- `netbackup` / `backup`: NetBackup configuration
- `chrony` / `time` / `ntp`: Time synchronization
- `dns` / `resolv`: DNS resolution configuration

## Logging

All operations are logged to `/var/log/ansible-postconfig.log` with:
- Timestamps for all operations
- Success/failure status
- Error details and troubleshooting information
- Configuration verification results

## Error Handling

The playbook includes comprehensive error handling:
- **Pre-flight Validation**: Checks all required variables
- **Graceful Failures**: Continues execution where possible
- **Backup Creation**: Automatic backups before changes
- **Verification**: Post-configuration validation
- **Rollback**: Manual rollback using backup files

## Security Features

- **Input Validation**: All parameters validated before use
- **No Hardcoded Secrets**: Configuration externalized to variables
- **Privilege Management**: Proper use of Ansible privilege escalation
- **File Permissions**: Correct permissions set on all configuration files
- **Backup Security**: Timestamped backups for audit trail

## Troubleshooting

### Common Issues

1. **Variable Validation Failures**
   - Check FQDN format and IP address validity
   - Ensure dc_location is HDC1 or HDC2
   - Verify Environment is production/staging/development

2. **Permission Errors**
   - Ensure Ansible user has sudo privileges
   - Check file system permissions on target directories

3. **Network Connectivity**
   - Verify DNS resolution is working
   - Check NTP server accessibility
   - Validate NetBackup server connectivity

### Debug Mode

Enable debug output for detailed troubleshooting:
```bash
-e debug_output=true
```

### Log Analysis

Check the log file for detailed operation history:
```bash
tail -f /var/log/ansible-postconfig.log
```

## Migration from v1

To migrate from v1 to v2:

1. **Update Playbook Calls**: Use new variable names with `var_` prefix
2. **Review Configuration**: Check externalized variables in `vars/main.yml`
3. **Test Execution**: Run with `debug_output=true` first
4. **Validate Results**: Verify all components are configured correctly

## Support

For issues or questions:
- Check the troubleshooting section above
- Review log files for detailed error information
- Contact the CES Operational Excellence Team

## Version History

- **v2.0**: Complete rewrite with Ansible best practices, enhanced error handling, and security improvements
- **v1.0**: Initial version with basic functionality
