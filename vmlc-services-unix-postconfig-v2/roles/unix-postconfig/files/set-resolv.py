#!/usr/bin/env python3
# Enhanced DNS resolv.conf configuration script
# Version 2.0 - Improved error handling, validation, and security

import sys
import os
import subprocess
import re
from datetime import datetime

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")

def validate_fqdn(fqdn):
    """Validate FQDN format"""
    if not fqdn or len(fqdn) > 253:
        return False

    # RFC 1123 FQDN validation
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return re.match(pattern, fqdn) is not None

def validate_dc_location(location):
    """Validate data center location parameter"""
    valid_locations = ['HDC1', 'HDC2']
    return location.upper() in valid_locations

def get_domain_from_fqdn(fqdn):
    """Extract domain from FQDN"""
    try:
        parts = fqdn.lower().split('.')
        if len(parts) > 1:
            return '.'.join(parts[1:])
        else:
            log_message(f"Invalid FQDN format: {fqdn}", "ERROR")
            return None
    except Exception as e:
        log_message(f"Exception extracting domain from FQDN: {e}", "ERROR")
        return None

def load_dns_configuration():
    """Load DNS server configuration for different domains and data centers"""
    return {
        'HDC1': {
            "healthgrp.com.sg": [
                "search healthgrp.com.sg",
                "# HDC1 AMK DNS",
                "nameserver 10.244.152.33",
                "nameserver 10.244.152.34",
                "# HDC2 FORT DNS",
                "nameserver 10.245.152.30",
                "nameserver 10.245.152.31"
            ],
            "sechealthgrp.com.sg": [
                "search sechealthgrp.com.sg",
                "# HDC1 AMK DNS",
                "nameserver 10.244.88.8",
                "nameserver 10.244.88.9",
                "# HDC2 FORT DNS",
                "nameserver 10.245.88.8",
                "nameserver 10.245.88.9"
            ],
            "devhealthgrp.com.sg": [
                "search devhealthgrp.com.sg",
                "# HDC1 AMK DNS",
                "nameserver 10.246.40.19",
                "# HDC2 FORT DNS",
                "nameserver 10.246.168.19"
            ],
            "healthgrpextp.com.sg": [
                "search healthgrpextp.com.sg",
                "# HDC1 AMK DNS",
                "nameserver 10.239.151.8",
                "nameserver 10.239.151.9",
                "# HDC2 FORT DNS",
                "nameserver 10.243.151.8",
                "nameserver 10.243.151.9"
            ],
            "hcloud.healthgrp.com.sg": [
                "search hcloud.healthgrp.com.sg",
                "# HDC1 AMK DNS",
                "nameserver 10.244.152.18",
                "nameserver 10.244.152.19",
                "# HDC2 FORT DNS",
                "nameserver 10.245.152.18",
                "nameserver 10.245.152.19"
            ],
            "iltc.healthgrp.com.sg": [
                "search iltc.healthgrp.com.sg",
                "# HDC1 AMK DNS (Nil)",
                "# HDC2 FORT DNS",
                "nameserver 10.240.152.19",
                "nameserver 10.240.152.20"
            ],
            "nhg.local": [
                "search nhg.local",
                "# HDC1 AMK DNS",
                "nameserver 10.237.152.30",
                "nameserver 10.237.152.31",
                "# HDC2 FORT DNS",
                "nameserver 10.240.152.36",
                "nameserver 10.240.152.37"
            ],
            "aic.local": [
                "search aic.local",
                "# HDC1 AMK DNS (Nil)",
                "# HDC2 FORT DNS",
                "nameserver 10.240.152.18",
                "nameserver 10.240.152.31"
            ]
        },
        'HDC2': {
            "healthgrp.com.sg": [
                "search healthgrp.com.sg",
                "# HDC2 FORT DNS",
                "nameserver 10.245.152.30",
                "nameserver 10.245.152.31",
                "# HDC1 AMK DNS",
                "nameserver 10.244.152.33",
                "nameserver 10.244.152.34"
            ],
            "sechealthgrp.com.sg": [
                "search sechealthgrp.com.sg",
                "# HDC2 FORT DNS",
                "nameserver 10.245.88.8",
                "nameserver 10.245.88.9",
                "# HDC1 AMK DNS",
                "nameserver 10.244.88.8",
                "nameserver 10.244.88.9"
            ],
            "devhealthgrp.com.sg": [
                "search devhealthgrp.com.sg",
                "# HDC2 FORT DNS",
                "nameserver 10.246.168.19",
                "# HDC1 AMK DNS",
                "nameserver 10.246.40.19"
            ],
            "healthgrpextp.com.sg": [
                "search healthgrpextp.com.sg",
                "# HDC2 FORT DNS",
                "nameserver 10.243.151.8",
                "nameserver 10.243.151.9",
                "# HDC1 AMK DNS",
                "nameserver 10.239.151.8",
                "nameserver 10.239.151.9"
            ],
            "hcloud.healthgrp.com.sg": [
                "search hcloud.healthgrp.com.sg",
                "# HDC2 FORT DNS",
                "nameserver 10.245.152.18",
                "nameserver 10.245.152.19",
                "# HDC1 AMK DNS",
                "nameserver 10.244.152.18",
                "nameserver 10.244.152.19"
            ],
            "iltc.healthgrp.com.sg": [
                "search iltc.healthgrp.com.sg",
                "# HDC2 FORT DNS",
                "nameserver 10.240.152.19",
                "nameserver 10.240.152.20",
                "# HDC1 AMK DNS (Nil)"
            ],
            "nhg.local": [
                "search nhg.local",
                "# HDC2 FORT DNS",
                "nameserver 10.240.152.36",
                "nameserver 10.240.152.37",
                "# HDC1 AMK DNS",
                "nameserver 10.237.152.30",
                "nameserver 10.237.152.31"
            ],
            "aic.local": [
                "search aic.local",
                "# HDC2 FORT DNS",
                "nameserver 10.240.152.18",
                "nameserver 10.240.152.31",
                "# HDC1 AMK DNS (Nil)"
            ]
        }
    }

def get_dns_entries(domain, dc_location):
    """Get DNS entries for specified domain and data center"""
    try:
        dns_config = load_dns_configuration()
        dc_location = dc_location.upper()

        if dc_location not in dns_config:
            log_message(f"Invalid DC location: {dc_location}", "ERROR")
            return None

        if domain not in dns_config[dc_location]:
            log_message(f"Domain not found: {domain}", "ERROR")
            return None

        return dns_config[dc_location][domain]

    except Exception as e:
        log_message(f"Exception getting DNS entries: {e}", "ERROR")
        return None

def backup_resolv_conf(file_path):
    """Create backup of existing resolv.conf file"""
    try:
        if os.path.exists(file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{file_path}.backup.{timestamp}"

            subprocess.run(['cp', file_path, backup_path],
                         check=True,
                         timeout=30)
            log_message(f"Backed up existing resolv.conf to {backup_path}")
            return backup_path
        else:
            log_message("No existing resolv.conf file to backup")
            return None

    except Exception as e:
        log_message(f"Failed to backup resolv.conf: {e}", "ERROR")
        return None

def read_existing_resolv_conf(file_path):
    """Read existing resolv.conf and filter out DNS entries"""
    try:
        if not os.path.exists(file_path):
            log_message("resolv.conf does not exist, will create new one")
            return []

        with open(file_path, 'r') as file:
            lines = file.readlines()

        # Filter out DNS-related lines
        filtered_lines = []
        for line in lines:
            stripped = line.strip()
            if (not stripped.startswith('nameserver') and
                not stripped.startswith('search') and
                not stripped.startswith('#')):
                filtered_lines.append(line)

        return filtered_lines

    except Exception as e:
        log_message(f"Failed to read existing resolv.conf: {e}", "ERROR")
        return []

def check_current_dns_config(file_path, expected_entries):
    """Check if current DNS configuration matches expected"""
    try:
        if not os.path.exists(file_path):
            return False

        with open(file_path, 'r') as file:
            lines = file.readlines()

        # Extract current DNS entries
        current_entries = []
        for line in lines:
            stripped = line.strip()
            if (stripped.startswith('search') or
                stripped.startswith('nameserver') or
                stripped.startswith('#')):
                current_entries.append(stripped)

        # Compare with expected entries
        return current_entries == expected_entries

    except Exception as e:
        log_message(f"Exception checking current DNS config: {e}", "ERROR")
        return False

def write_resolv_conf(file_path, fqdn, dc_location):
    """Write new resolv.conf with DNS configuration"""
    try:
        domain = get_domain_from_fqdn(fqdn)
        if not domain:
            return False

        dns_entries = get_dns_entries(domain, dc_location)
        if not dns_entries:
            return False

        # Check if configuration is already correct
        if check_current_dns_config(file_path, dns_entries):
            log_message(f"No changes needed for {fqdn} with {dc_location}")
            return True

        # Read existing non-DNS configuration
        existing_lines = read_existing_resolv_conf(file_path)

        # Build new configuration
        new_lines = []

        # Add existing non-DNS lines
        new_lines.extend(existing_lines)

        # Add DNS entries
        for entry in dns_entries:
            new_lines.append(entry + '\n')

        # Write to temporary file first
        temp_file = f"{file_path}.tmp"
        with open(temp_file, 'w') as file:
            file.writelines(new_lines)

        # Validate temporary file
        if os.path.exists(temp_file) and os.path.getsize(temp_file) >= 0:
            # Move to final location
            subprocess.run(['mv', temp_file, file_path],
                         check=True,
                         timeout=30)
            subprocess.run(['chmod', '644', file_path],
                         check=True,
                         timeout=30)
            log_message(f"Successfully updated resolv.conf for {fqdn} with {dc_location}")
            return True
        else:
            log_message("Temporary resolv.conf file validation failed", "ERROR")
            return False

    except Exception as e:
        log_message(f"Failed to write resolv.conf: {e}", "ERROR")
        return False

def verify_resolv_conf(file_path, fqdn, dc_location):
    """Verify resolv.conf was configured correctly"""
    try:
        if not os.path.exists(file_path):
            log_message("resolv.conf file does not exist after creation", "ERROR")
            return False

        domain = get_domain_from_fqdn(fqdn)
        if not domain:
            return False

        dns_entries = get_dns_entries(domain, dc_location)
        if not dns_entries:
            return False

        with open(file_path, 'r') as file:
            content = file.read()

        # Check for search domain
        search_line = f"search {domain}"
        if search_line not in content:
            log_message(f"Search domain '{domain}' not found in resolv.conf", "ERROR")
            return False

        # Count nameserver entries
        nameserver_count = content.count('nameserver ')
        if nameserver_count == 0:
            log_message("No nameserver entries found in resolv.conf", "ERROR")
            return False

        log_message(f"resolv.conf verification successful: {nameserver_count} nameservers configured for {domain}")
        return True

    except Exception as e:
        log_message(f"Failed to verify resolv.conf: {e}", "ERROR")
        return False

def test_dns_resolution(fqdn):
    """Test DNS resolution for the FQDN"""
    try:
        result = subprocess.run(['nslookup', fqdn],
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=30)

        if result.returncode == 0:
            log_message(f"DNS resolution test successful for {fqdn}")
            return True
        else:
            log_message(f"DNS resolution test failed for {fqdn}: {result.stderr}", "WARNING")
            return False

    except subprocess.TimeoutExpired:
        log_message(f"Timeout testing DNS resolution for {fqdn}", "WARNING")
        return False
    except FileNotFoundError:
        log_message("nslookup not found, skipping DNS resolution test", "WARNING")
        return True  # Don't fail if nslookup is not available
    except Exception as e:
        log_message(f"Exception testing DNS resolution: {e}", "WARNING")
        return False

def main():
    """Main function with enhanced error handling"""
    if len(sys.argv) != 3:
        log_message("Usage: python set-resolv.py <FQDN> <HDC1|HDC2>", "ERROR")
        sys.exit(1)

    fqdn = sys.argv[1].strip()
    dc_location = sys.argv[2].strip().upper()
    file_path = "/etc/resolv.conf"

    # Validate inputs
    if not validate_fqdn(fqdn):
        log_message(f"Invalid FQDN format: {fqdn}", "ERROR")
        sys.exit(1)

    if not validate_dc_location(dc_location):
        log_message(f"Invalid DC location: {dc_location}. Must be HDC1 or HDC2", "ERROR")
        sys.exit(1)

    log_message(f"Starting DNS configuration for {fqdn} at {dc_location}")

    # Create backup
    backup_resolv_conf(file_path)

    # Write new configuration
    if not write_resolv_conf(file_path, fqdn, dc_location):
        log_message("Failed to write resolv.conf", "ERROR")
        sys.exit(1)

    # Verify configuration
    if not verify_resolv_conf(file_path, fqdn, dc_location):
        log_message("resolv.conf verification failed", "ERROR")
        sys.exit(1)

    # Test DNS resolution
    test_dns_resolution(fqdn)

    log_message("DNS configuration completed successfully")
    sys.exit(0)

if __name__ == "__main__":
    main()
