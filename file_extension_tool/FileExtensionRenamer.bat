@echo off
setlocal enabledelayedexpansion

:: File Extension Renamer Tool
:: Author: <PERSON> - CES Operational Excellence Team
:: Description: A batch script with GUI to rename file extensions to .txt and back

:: Check for admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click and select "Run as administrator".
    pause
    exit /b 1
)

:: Create a temporary PowerShell script for the GUI
echo Creating GUI interface...
set "tempScript=%TEMP%\ExtensionRenamerGUI.ps1"

echo Add-Type -AssemblyName System.Windows.Forms > "%tempScript%"
echo Add-Type -AssemblyName System.Drawing >> "%tempScript%"

:: Create the form
echo $form = New-Object System.Windows.Forms.Form >> "%tempScript%"
echo $form.Text = 'File Extension Renamer Tool' >> "%tempScript%"
echo $form.Size = New-Object System.Drawing.Size(700, 600) >> "%tempScript%"
echo $form.StartPosition = 'CenterScreen' >> "%tempScript%"
echo $form.FormBorderStyle = 'FixedDialog' >> "%tempScript%"
echo $form.MaximizeBox = $false >> "%tempScript%"

:: Create a title label
echo $titleLabel = New-Object System.Windows.Forms.Label >> "%tempScript%"
echo $titleLabel.Location = New-Object System.Drawing.Point(20, 20) >> "%tempScript%"
echo $titleLabel.Size = New-Object System.Drawing.Size(660, 30) >> "%tempScript%"
echo $titleLabel.Text = 'File Extension Renamer Tool for Air Gap Solutions' >> "%tempScript%"
echo $titleLabel.Font = New-Object System.Drawing.Font('Arial', 14, [System.Drawing.FontStyle]::Bold) >> "%tempScript%"
echo $titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter >> "%tempScript%"
echo $form.Controls.Add($titleLabel) >> "%tempScript%"

:: Create a subtitle label
echo $subtitleLabel = New-Object System.Windows.Forms.Label >> "%tempScript%"
echo $subtitleLabel.Location = New-Object System.Drawing.Point(20, 50) >> "%tempScript%"
echo $subtitleLabel.Size = New-Object System.Drawing.Size(660, 20) >> "%tempScript%"
echo $subtitleLabel.Text = 'Convert file extensions to .txt for air-gapped environments' >> "%tempScript%"
echo $subtitleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter >> "%tempScript%"
echo $form.Controls.Add($subtitleLabel) >> "%tempScript%"

:: Create a group box for source selection
echo $sourceGroupBox = New-Object System.Windows.Forms.GroupBox >> "%tempScript%"
echo $sourceGroupBox.Location = New-Object System.Drawing.Point(20, 80) >> "%tempScript%"
echo $sourceGroupBox.Size = New-Object System.Drawing.Size(660, 100) >> "%tempScript%"
echo $sourceGroupBox.Text = 'Source Selection' >> "%tempScript%"
echo $form.Controls.Add($sourceGroupBox) >> "%tempScript%"

:: Create radio buttons for selection type
echo $fileRadioButton = New-Object System.Windows.Forms.RadioButton >> "%tempScript%"
echo $fileRadioButton.Location = New-Object System.Drawing.Point(20, 30) >> "%tempScript%"
echo $fileRadioButton.Size = New-Object System.Drawing.Size(100, 20) >> "%tempScript%"
echo $fileRadioButton.Text = 'Single File' >> "%tempScript%"
echo $fileRadioButton.Checked = $true >> "%tempScript%"
echo $sourceGroupBox.Controls.Add($fileRadioButton) >> "%tempScript%"

echo $folderRadioButton = New-Object System.Windows.Forms.RadioButton >> "%tempScript%"
echo $folderRadioButton.Location = New-Object System.Drawing.Point(130, 30) >> "%tempScript%"
echo $folderRadioButton.Size = New-Object System.Drawing.Size(100, 20) >> "%tempScript%"
echo $folderRadioButton.Text = 'Folder' >> "%tempScript%"
echo $sourceGroupBox.Controls.Add($folderRadioButton) >> "%tempScript%"

:: Create source path textbox and browse button
echo $sourcePathLabel = New-Object System.Windows.Forms.Label >> "%tempScript%"
echo $sourcePathLabel.Location = New-Object System.Drawing.Point(20, 60) >> "%tempScript%"
echo $sourcePathLabel.Size = New-Object System.Drawing.Size(80, 20) >> "%tempScript%"
echo $sourcePathLabel.Text = 'Source Path:' >> "%tempScript%"
echo $sourceGroupBox.Controls.Add($sourcePathLabel) >> "%tempScript%"

echo $sourcePathTextBox = New-Object System.Windows.Forms.TextBox >> "%tempScript%"
echo $sourcePathTextBox.Location = New-Object System.Drawing.Point(110, 60) >> "%tempScript%"
echo $sourcePathTextBox.Size = New-Object System.Drawing.Size(440, 20) >> "%tempScript%"
echo $sourcePathTextBox.ReadOnly = $true >> "%tempScript%"
echo $sourceGroupBox.Controls.Add($sourcePathTextBox) >> "%tempScript%"

echo $sourceBrowseButton = New-Object System.Windows.Forms.Button >> "%tempScript%"
echo $sourceBrowseButton.Location = New-Object System.Drawing.Point(560, 59) >> "%tempScript%"
echo $sourceBrowseButton.Size = New-Object System.Drawing.Size(80, 23) >> "%tempScript%"
echo $sourceBrowseButton.Text = 'Browse' >> "%tempScript%"
echo $sourceGroupBox.Controls.Add($sourceBrowseButton) >> "%tempScript%"

:: Create a group box for operation selection
echo $operationGroupBox = New-Object System.Windows.Forms.GroupBox >> "%tempScript%"
echo $operationGroupBox.Location = New-Object System.Drawing.Point(20, 190) >> "%tempScript%"
echo $operationGroupBox.Size = New-Object System.Drawing.Size(660, 80) >> "%tempScript%"
echo $operationGroupBox.Text = 'Operation' >> "%tempScript%"
echo $form.Controls.Add($operationGroupBox) >> "%tempScript%"

:: Create radio buttons for operation type
echo $convertRadioButton = New-Object System.Windows.Forms.RadioButton >> "%tempScript%"
echo $convertRadioButton.Location = New-Object System.Drawing.Point(20, 30) >> "%tempScript%"
echo $convertRadioButton.Size = New-Object System.Drawing.Size(200, 20) >> "%tempScript%"
echo $convertRadioButton.Text = 'Convert to .txt' >> "%tempScript%"
echo $convertRadioButton.Checked = $true >> "%tempScript%"
echo $operationGroupBox.Controls.Add($convertRadioButton) >> "%tempScript%"

echo $restoreRadioButton = New-Object System.Windows.Forms.RadioButton >> "%tempScript%"
echo $restoreRadioButton.Location = New-Object System.Drawing.Point(230, 30) >> "%tempScript%"
echo $restoreRadioButton.Size = New-Object System.Drawing.Size(200, 20) >> "%tempScript%"
echo $restoreRadioButton.Text = 'Restore original extensions' >> "%tempScript%"
echo $operationGroupBox.Controls.Add($restoreRadioButton) >> "%tempScript%"

:: Create a group box for options
echo $optionsGroupBox = New-Object System.Windows.Forms.GroupBox >> "%tempScript%"
echo $optionsGroupBox.Location = New-Object System.Drawing.Point(20, 280) >> "%tempScript%"
echo $optionsGroupBox.Size = New-Object System.Drawing.Size(660, 80) >> "%tempScript%"
echo $optionsGroupBox.Text = 'Options' >> "%tempScript%"
echo $form.Controls.Add($optionsGroupBox) >> "%tempScript%"

:: Create checkbox for recursive operation
echo $recursiveCheckBox = New-Object System.Windows.Forms.CheckBox >> "%tempScript%"
echo $recursiveCheckBox.Location = New-Object System.Drawing.Point(20, 30) >> "%tempScript%"
echo $recursiveCheckBox.Size = New-Object System.Drawing.Size(300, 20) >> "%tempScript%"
echo $recursiveCheckBox.Text = 'Process subfolders (recursive)' >> "%tempScript%"
echo $recursiveCheckBox.Checked = $true >> "%tempScript%"
echo $optionsGroupBox.Controls.Add($recursiveCheckBox) >> "%tempScript%"

:: Create a group box for log output
echo $logGroupBox = New-Object System.Windows.Forms.GroupBox >> "%tempScript%"
echo $logGroupBox.Location = New-Object System.Drawing.Point(20, 370) >> "%tempScript%"
echo $logGroupBox.Size = New-Object System.Drawing.Size(660, 150) >> "%tempScript%"
echo $logGroupBox.Text = 'Operation Log' >> "%tempScript%"
echo $form.Controls.Add($logGroupBox) >> "%tempScript%"

:: Create log textbox
echo $logTextBox = New-Object System.Windows.Forms.TextBox >> "%tempScript%"
echo $logTextBox.Location = New-Object System.Drawing.Point(10, 20) >> "%tempScript%"
echo $logTextBox.Size = New-Object System.Drawing.Size(640, 120) >> "%tempScript%"
echo $logTextBox.Multiline = $true >> "%tempScript%"
echo $logTextBox.ScrollBars = 'Vertical' >> "%tempScript%"
echo $logTextBox.ReadOnly = $true >> "%tempScript%"
echo $logGroupBox.Controls.Add($logTextBox) >> "%tempScript%"

:: Create execute button
echo $executeButton = New-Object System.Windows.Forms.Button >> "%tempScript%"
echo $executeButton.Location = New-Object System.Drawing.Point(250, 530) >> "%tempScript%"
echo $executeButton.Size = New-Object System.Drawing.Size(200, 30) >> "%tempScript%"
echo $executeButton.Text = 'Execute' >> "%tempScript%"
echo $executeButton.Font = New-Object System.Drawing.Font('Arial', 10, [System.Drawing.FontStyle]::Bold) >> "%tempScript%"
echo $form.Controls.Add($executeButton) >> "%tempScript%"

:: Add event handlers
echo # Browse button click event >> "%tempScript%"
echo $sourceBrowseButton.Add_Click({ >> "%tempScript%"
echo     if ($fileRadioButton.Checked) { >> "%tempScript%"
echo         $openFileDialog = New-Object System.Windows.Forms.OpenFileDialog >> "%tempScript%"
echo         $openFileDialog.Title = "Select a file" >> "%tempScript%"
echo         $openFileDialog.Filter = "All files (*.*)|*.*" >> "%tempScript%"
echo         if ($openFileDialog.ShowDialog() -eq 'OK') { >> "%tempScript%"
echo             $sourcePathTextBox.Text = $openFileDialog.FileName >> "%tempScript%"
echo             $logTextBox.AppendText("Selected file: $($openFileDialog.FileName)`r`n") >> "%tempScript%"
echo         } >> "%tempScript%"
echo     } else { >> "%tempScript%"
echo         $folderBrowserDialog = New-Object System.Windows.Forms.FolderBrowserDialog >> "%tempScript%"
echo         $folderBrowserDialog.Description = "Select a folder" >> "%tempScript%"
echo         if ($folderBrowserDialog.ShowDialog() -eq 'OK') { >> "%tempScript%"
echo             $sourcePathTextBox.Text = $folderBrowserDialog.SelectedPath >> "%tempScript%"
echo             $logTextBox.AppendText("Selected folder: $($folderBrowserDialog.SelectedPath)`r`n") >> "%tempScript%"
echo         } >> "%tempScript%"
echo     } >> "%tempScript%"
echo }) >> "%tempScript%"

:: Execute button click event
echo $executeButton.Add_Click({ >> "%tempScript%"
echo     $sourcePath = $sourcePathTextBox.Text >> "%tempScript%"
echo     if (-not $sourcePath) { >> "%tempScript%"
echo         [System.Windows.Forms.MessageBox]::Show("Please select a source file or folder.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error) >> "%tempScript%"
echo         return >> "%tempScript%"
echo     } >> "%tempScript%"
echo     >> "%tempScript%"
echo     $isFile = $fileRadioButton.Checked >> "%tempScript%"
echo     $isConvert = $convertRadioButton.Checked >> "%tempScript%"
echo     $isRecursive = $recursiveCheckBox.Checked >> "%tempScript%"
echo     >> "%tempScript%"
echo     $logTextBox.Clear() >> "%tempScript%"
echo     $logTextBox.AppendText("Starting operation...`r`n") >> "%tempScript%"
echo     >> "%tempScript%"
echo     # Create a timestamp for the log file name >> "%tempScript%"
echo     $timestamp = Get-Date -Format "yyyyMMdd_HHmmss" >> "%tempScript%"
echo     $logDir = Join-Path $PSScriptRoot "logs" >> "%tempScript%"
echo     >> "%tempScript%"
echo     # Create logs directory if it doesn't exist >> "%tempScript%"
echo     if (-not (Test-Path $logDir)) { >> "%tempScript%"
echo         New-Item -ItemType Directory -Path $logDir -Force | Out-Null >> "%tempScript%"
echo     } >> "%tempScript%"
echo     >> "%tempScript%"
echo     $logFile = Join-Path $logDir "extension_tool_${timestamp}.log" >> "%tempScript%"
echo     >> "%tempScript%"
echo     # Function to log messages >> "%tempScript%"
echo     function Log-Message { >> "%tempScript%"
echo         param([string]$message) >> "%tempScript%"
echo         $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss" >> "%tempScript%"
echo         $logMessage = "[$timestamp] $message" >> "%tempScript%"
echo         $logTextBox.AppendText("$logMessage`r`n") >> "%tempScript%"
echo         Add-Content -Path $logFile -Value $logMessage >> "%tempScript%"
echo         # Force UI update >> "%tempScript%"
echo         [System.Windows.Forms.Application]::DoEvents() >> "%tempScript%"
echo     } >> "%tempScript%"
echo     >> "%tempScript%"
echo     Log-Message "Operation started" >> "%tempScript%"
echo     >> "%tempScript%"
echo     # Function to convert file extensions to .txt >> "%tempScript%"
echo     function Convert-ToTxt { >> "%tempScript%"
echo         param( >> "%tempScript%"
echo             [string]$path, >> "%tempScript%"
echo             [bool]$isFile, >> "%tempScript%"
echo             [bool]$isRecursive >> "%tempScript%"
echo         ) >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Create or load extension map >> "%tempScript%"
echo         $mapFile = Join-Path (Split-Path $path -Parent) "extension_map.json" >> "%tempScript%"
echo         if (Test-Path $mapFile) { >> "%tempScript%"
echo             $extensionMap = Get-Content $mapFile -Raw | ConvertFrom-Json >> "%tempScript%"
echo             Log-Message "Loaded existing extension map from $mapFile" >> "%tempScript%"
echo         } else { >> "%tempScript%"
echo             $extensionMap = @{} >> "%tempScript%"
echo             Log-Message "Created new extension map" >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Process a single file >> "%tempScript%"
echo         function Process-File { >> "%tempScript%"
echo             param([string]$filePath) >> "%tempScript%"
echo             >> "%tempScript%"
echo             # Skip if already .txt or if it's the map file >> "%tempScript%"
echo             if ($filePath -like "*.txt" -or $filePath -eq $mapFile) { >> "%tempScript%"
echo                 Log-Message "Skipping $filePath (already .txt or map file)" >> "%tempScript%"
echo                 return >> "%tempScript%"
echo             } >> "%tempScript%"
echo             >> "%tempScript%"
echo             try { >> "%tempScript%"
echo                 $fileInfo = New-Object System.IO.FileInfo($filePath) >> "%tempScript%"
echo                 $originalExt = $fileInfo.Extension >> "%tempScript%"
echo                 $newPath = [System.IO.Path]::ChangeExtension($filePath, ".txt") >> "%tempScript%"
echo                 >> "%tempScript%"
echo                 # Store the original extension in the map >> "%tempScript%"
echo                 $relativePath = $filePath.Replace("$([System.IO.Path]::GetDirectoryName($mapFile))\", "") >> "%tempScript%"
echo                 $extensionMap[$relativePath] = $originalExt >> "%tempScript%"
echo                 >> "%tempScript%"
echo                 # Rename the file >> "%tempScript%"
echo                 Rename-Item -Path $filePath -NewName $newPath -Force >> "%tempScript%"
echo                 Log-Message "Converted: $filePath -> $newPath" >> "%tempScript%"
echo             } catch { >> "%tempScript%"
echo                 Log-Message "Error processing $filePath`: $_" >> "%tempScript%"
echo             } >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Process files based on input type >> "%tempScript%"
echo         if ($isFile) { >> "%tempScript%"
echo             Process-File -filePath $path >> "%tempScript%"
echo         } else { >> "%tempScript%"
echo             # Process folder >> "%tempScript%"
echo             $searchOption = if ($isRecursive) { "AllDirectories" } else { "TopDirectoryOnly" } >> "%tempScript%"
echo             $files = [System.IO.Directory]::GetFiles($path, "*.*", [System.IO.SearchOption]::$searchOption) >> "%tempScript%"
echo             >> "%tempScript%"
echo             Log-Message "Found $($files.Count) files to process" >> "%tempScript%"
echo             >> "%tempScript%"
echo             $processedCount = 0 >> "%tempScript%"
echo             foreach ($file in $files) { >> "%tempScript%"
echo                 Process-File -filePath $file >> "%tempScript%"
echo                 $processedCount++ >> "%tempScript%"
echo                 if ($processedCount % 10 -eq 0) { >> "%tempScript%"
echo                     Log-Message "Processed $processedCount of $($files.Count) files" >> "%tempScript%"
echo                 } >> "%tempScript%"
echo             } >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Save the extension map >> "%tempScript%"
echo         $extensionMap | ConvertTo-Json | Set-Content -Path $mapFile >> "%tempScript%"
echo         Log-Message "Saved extension map to $mapFile" >> "%tempScript%"
echo         Log-Message "Conversion completed successfully" >> "%tempScript%"
echo     } >> "%tempScript%"
echo     >> "%tempScript%"
echo     # Function to restore original extensions >> "%tempScript%"
echo     function Restore-Extensions { >> "%tempScript%"
echo         param( >> "%tempScript%"
echo             [string]$path, >> "%tempScript%"
echo             [bool]$isFile >> "%tempScript%"
echo         ) >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Find the map file >> "%tempScript%"
echo         $mapFile = if ($isFile) { >> "%tempScript%"
echo             Join-Path (Split-Path $path -Parent) "extension_map.json" >> "%tempScript%"
echo         } else { >> "%tempScript%"
echo             Join-Path $path "extension_map.json" >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         if (-not (Test-Path $mapFile)) { >> "%tempScript%"
echo             Log-Message "Error: Extension map file not found at $mapFile" >> "%tempScript%"
echo             [System.Windows.Forms.MessageBox]::Show("Extension map file not found. Cannot restore original extensions.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error) >> "%tempScript%"
echo             return >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Load the extension map >> "%tempScript%"
echo         $extensionMap = Get-Content $mapFile -Raw | ConvertFrom-Json >> "%tempScript%"
echo         Log-Message "Loaded extension map from $mapFile" >> "%tempScript%"
echo         >> "%tempScript%"
echo         $baseDir = Split-Path $mapFile -Parent >> "%tempScript%"
echo         $restoredCount = 0 >> "%tempScript%"
echo         $totalFiles = ($extensionMap | Get-Member -MemberType NoteProperty).Count >> "%tempScript%"
echo         >> "%tempScript%"
echo         Log-Message "Found $totalFiles files to restore" >> "%tempScript%"
echo         >> "%tempScript%"
echo         # Process each file in the map >> "%tempScript%"
echo         foreach ($item in $extensionMap.PSObject.Properties) { >> "%tempScript%"
echo             $relativePath = $item.Name >> "%tempScript%"
echo             $originalExt = $item.Value >> "%tempScript%"
echo             >> "%tempScript%"
echo             # Get the current .txt file path >> "%tempScript%"
echo             $txtFilePath = Join-Path $baseDir $relativePath >> "%tempScript%"
echo             $txtFilePath = [System.IO.Path]::ChangeExtension($txtFilePath, ".txt") >> "%tempScript%"
echo             >> "%tempScript%"
echo             # Skip if we're processing a single file and it's not the one we want >> "%tempScript%"
echo             if ($isFile -and $txtFilePath -ne [System.IO.Path]::ChangeExtension($path, ".txt")) { >> "%tempScript%"
echo                 continue >> "%tempScript%"
echo             } >> "%tempScript%"
echo             >> "%tempScript%"
echo             if (Test-Path $txtFilePath) { >> "%tempScript%"
echo                 try { >> "%tempScript%"
echo                     # Get the original file path with its extension >> "%tempScript%"
echo                     $originalFilePath = [System.IO.Path]::ChangeExtension($txtFilePath, $originalExt) >> "%tempScript%"
echo                     >> "%tempScript%"
echo                     # Rename the file back to its original extension >> "%tempScript%"
echo                     Rename-Item -Path $txtFilePath -NewName $originalFilePath -Force >> "%tempScript%"
echo                     Log-Message "Restored: $txtFilePath -> $originalFilePath" >> "%tempScript%"
echo                     $restoredCount++ >> "%tempScript%"
echo                 } catch { >> "%tempScript%"
echo                     Log-Message "Error restoring $txtFilePath`: $_" >> "%tempScript%"
echo                 } >> "%tempScript%"
echo             } else { >> "%tempScript%"
echo                 Log-Message "Warning: File not found for restoration: $txtFilePath" >> "%tempScript%"
echo             } >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         Log-Message "Restored $restoredCount of $totalFiles files" >> "%tempScript%"
echo         >> "%tempScript%"
echo         # If we're processing a folder and all files were restored, delete the map file >> "%tempScript%"
echo         if (-not $isFile -and $restoredCount -eq $totalFiles) { >> "%tempScript%"
echo             Remove-Item -Path $mapFile -Force >> "%tempScript%"
echo             Log-Message "Removed extension map file" >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         Log-Message "Restoration completed successfully" >> "%tempScript%"
echo     } >> "%tempScript%"
echo     >> "%tempScript%"
echo     # Execute the selected operation >> "%tempScript%"
echo     try { >> "%tempScript%"
echo         if ($isConvert) { >> "%tempScript%"
echo             Log-Message "Starting conversion to .txt..." >> "%tempScript%"
echo             Convert-ToTxt -path $sourcePath -isFile $isFile -isRecursive $isRecursive >> "%tempScript%"
echo         } else { >> "%tempScript%"
echo             Log-Message "Starting restoration of original extensions..." >> "%tempScript%"
echo             Restore-Extensions -path $sourcePath -isFile $isFile >> "%tempScript%"
echo         } >> "%tempScript%"
echo         >> "%tempScript%"
echo         Log-Message "Operation completed successfully" >> "%tempScript%"
echo         Log-Message "Log file saved to: $logFile" >> "%tempScript%"
echo         [System.Windows.Forms.MessageBox]::Show("Operation completed successfully. Log file saved to: $logFile", "Success", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information) >> "%tempScript%"
echo     } catch { >> "%tempScript%"
echo         Log-Message "Error: $_" >> "%tempScript%"
echo         [System.Windows.Forms.MessageBox]::Show("An error occurred: $_", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error) >> "%tempScript%"
echo     } >> "%tempScript%"
echo }) >> "%tempScript%"

:: Show the form
echo $form.Add_Shown({$form.Activate()}) >> "%tempScript%"
echo [void]$form.ShowDialog() >> "%tempScript%"

:: Run the PowerShell script
echo Running GUI application...
powershell -ExecutionPolicy Bypass -File "%tempScript%"

:: Clean up
del "%tempScript%" >nul 2>&1

exit /b 0
