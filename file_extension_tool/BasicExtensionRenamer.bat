@echo off
setlocal enabledelayedexpansion

:: Basic File Extension Renamer Tool
:: Author: <PERSON> - CES Operational Excellence Team
:: Description: A simple batch script to rename file extensions to .txt and back

title Basic File Extension Renamer

:: Main menu
:menu
cls
echo ===================================================
echo           BASIC FILE EXTENSION RENAMER
echo ===================================================
echo.
echo  This tool helps you prepare files for air-gapped
echo  environments by converting extensions to .txt
echo.
echo  1. Convert file extensions to .txt
echo  2. Restore original file extensions
echo  3. Exit
echo.
echo ===================================================
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto convert
if "%choice%"=="2" goto restore
if "%choice%"=="3" exit /b 0
goto menu

:: Convert file extensions to .txt
:convert
cls
echo ===================================================
echo           CONVERT EXTENSIONS TO .TXT
echo ===================================================
echo.
echo  Please select a folder to process.
echo.
echo  NOTE: This will convert all file extensions in the
echo        selected folder to .txt and create a mapping
echo        file for later restoration.
echo.
echo ===================================================
echo.

:: Get folder path from user
echo Enter the full path of the folder to process:
echo (Example: C:\MyFiles\Documents)
echo.
set /p folder="Folder path: "

:: Check if folder exists
if not exist "%folder%" (
    echo.
    echo Error: The folder "%folder%" does not exist.
    echo Please check the path and try again.
    echo.
    pause
    goto menu
)

:: Ask if user wants to process subfolders
echo.
echo Do you want to process subfolders as well? (Y/N)
set /p process_subfolders="Your choice: "

:: Create the extension map file
set "map_file=%folder%\extension_map.txt"
if exist "%map_file%" del "%map_file%"

echo Starting conversion in folder: %folder%
echo Processing subfolders: %process_subfolders%

:: Process files
set "file_count=0"
set "processed_count=0"

if /i "%process_subfolders%"=="Y" (
    for /r "%folder%" %%f in (*) do (
        set /a file_count+=1
    )
    
    echo Found %file_count% files to process (including subfolders)
    
    for /r "%folder%" %%f in (*) do (
        call :process_file "%%f"
    )
) else (
    for %%f in ("%folder%\*") do (
        set /a file_count+=1
    )
    
    echo Found %file_count% files to process
    
    for %%f in ("%folder%\*") do (
        call :process_file "%%f"
    )
)

echo Conversion completed. Processed %processed_count% of %file_count% files.
echo Extension map saved to: %map_file%

echo.
echo Conversion completed successfully!
echo.
echo Press any key to return to the main menu...
pause > nul
goto menu

:: Process a single file
:process_file
set "filepath=%~1"
set "filename=%~nx1"
set "extension=%~x1"

:: Verify the file exists
if not exist "%filepath%" (
    echo Error: File not found: %filepath%
    goto :EOF
)

:: Skip if already .txt or if it's the map file
if /i "%extension%"==".txt" goto :EOF
if /i "%filepath%"=="%map_file%" goto :EOF

:: Store the mapping
echo %filepath%=%extension% >> "%map_file%" 2>nul

:: Rename the file
ren "%filepath%" "%filename:.=%.txt" 2>nul
if %errorlevel% equ 0 (
    echo Converted: %filepath% -^> %filename:.=%.txt
    set /a processed_count+=1
) else (
    echo Error converting: %filepath% (File may be in use or you don't have permission)
)
goto :EOF

:: Restore original file extensions
:restore
cls
echo ===================================================
echo           RESTORE ORIGINAL EXTENSIONS
echo ===================================================
echo.
echo  Please select the folder containing files to restore.
echo  This folder should contain the extension_map.txt file.
echo.
echo ===================================================
echo.

:: Get folder path from user
echo Enter the full path of the folder containing files to restore:
echo (Example: C:\MyFiles\Documents)
echo.
set /p folder="Folder path: "

:: Check if folder exists
if not exist "%folder%" (
    echo.
    echo Error: The folder "%folder%" does not exist.
    echo Please check the path and try again.
    echo.
    pause
    goto menu
)

:: Check if the map file exists
set "map_file=%folder%\extension_map.txt"
if not exist "%map_file%" (
    echo.
    echo Error: extension_map.txt not found in the selected folder.
    echo Cannot restore original extensions without the mapping file.
    echo.
    pause
    goto menu
)

echo Starting restoration from folder: %folder%
echo Using map file: %map_file%

:: Process the map file
set "restored_count=0"
set "total_files=0"

:: Count total files to restore
for /f "tokens=*" %%a in ('type "%map_file%"') do (
    set /a total_files+=1
)

echo Found %total_files% files to restore

:: Process each line in the map file
for /f "tokens=1,2 delims==" %%a in ('type "%map_file%"') do (
    set "txt_file=%%a"
    set "original_ext=%%b"
    
    :: Get the file path with .txt extension
    set "txt_path=!txt_file:.=!.txt"
    
    :: Check if the .txt file exists
    if exist "!txt_path!" (
        :: Get the file path without .txt extension
        set "original_file=!txt_file!"
        
        :: Rename the file back to its original extension
        ren "!txt_path!" "!original_file:~-1!!original_ext!" 2>nul
        if !errorlevel! equ 0 (
            echo Restored: !txt_path! -^> !original_file:~-1!!original_ext!
            set /a restored_count+=1
        ) else (
            echo Error restoring: !txt_path!
        )
    ) else (
        echo Warning: File not found for restoration: !txt_path!
    )
)

echo Restoration completed. Restored %restored_count% of %total_files% files.

:: Delete the map file if all files were restored
if %restored_count% equ %total_files% (
    del "%map_file%" 2>nul
    echo Removed extension map file
)

echo.
echo Restoration completed successfully!
echo.
echo Press any key to return to the main menu...
pause > nul
goto menu
