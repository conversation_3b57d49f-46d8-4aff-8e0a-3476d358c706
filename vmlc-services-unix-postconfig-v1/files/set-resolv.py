#!/usr/bin/env python3

import sys

def get_domain_from_fqdn(fqdn):
    return '.'.join(fqdn.lower().split('.')[1:])

def get_new_entries(domain, dc_location):
    hdc1_entries = {
        "healthgrp.com.sg": [
            "search healthgrp.com.sg",
            "# HDC1 AMK DNS",
            "nameserver 10.244.152.33",
            "nameserver 10.244.152.34",
            "# HDC2 FORT DNS",
            "nameserver 10.245.152.30",
            "nameserver 10.245.152.31"
        ],
        "sechealthgrp.com.sg": [
            "search sechealthgrp.com.sg",
            "# HDC1 AMK DNS",
            "nameserver 10.244.88.8",
            "nameserver 10.244.88.9",
            "# HDC2 FORT DNS",
            "nameserver 10.245.88.8",
            "nameserver 10.245.88.9"
        ],
        "devhealthgrp.com.sg": [
            "search devhealthgrp.com.sg",
            "# HDC1 AMK DNS",
            "nameserver 10.246.40.19",
            "# HDC2 FORT DNS",
            "nameserver 10.246.168.19"
        ],
        "healthgrpextp.com.sg": [
            "search healthgrpextp.com.sg",
            "# HDC1 AMK DNS",
            "nameserver 10.239.151.8",
            "nameserver 10.239.151.9",
            "# HDC2 FORT DNS",
            "nameserver 10.243.151.8",
            "nameserver 10.243.151.9"
        ],
        "hcloud.healthgrp.com.sg": [
            "search hcloud.healthgrp.com.sg",
            "# HDC1 AMK DNS",
            "nameserver 10.244.152.18",
            "nameserver 10.244.152.19",
            "# HDC2 FORT DNS",
            "nameserver 10.245.152.18",
            "nameserver 10.245.152.19"
        ],
        "iltc.healthgrp.com.sg": [
            "search iltc.healthgrp.com.sg",
            "# HDC1 AMK DNS (Nil)",
            "# HDC2 FORT DNS",
            "nameserver 10.240.152.19",
            "nameserver 10.240.152.20"
        ],
        "nhg.local": [
            "search nhg.local",
            "# HDC1 AMK DNS",
            "nameserver 10.237.152.30",
            "nameserver 10.237.152.31",
            "# HDC2 FORT DNS",
            "nameserver 10.240.152.36",
            "nameserver 10.240.152.37"
        ],
        "aic.local": [
            "search aic.local",
            "# HDC1 AMK DNS (Nil)",
            "# HDC2 FORT DNS",
            "nameserver 10.240.152.18",
            "nameserver 10.240.152.31"
        ]
    }
    
    hdc2_entries = {
        "healthgrp.com.sg": [
            "search healthgrp.com.sg",
            "# HDC2 FORT DNS",
            "nameserver 10.245.152.30",
            "nameserver 10.245.152.31",
            "# HDC1 AMK DNS",
            "nameserver 10.244.152.33",
            "nameserver 10.244.152.34"
        ],
        "sechealthgrp.com.sg": [
            "search sechealthgrp.com.sg",
            "# HDC2 FORT DNS",
            "nameserver 10.245.88.8",
            "nameserver 10.245.88.9",
            "# HDC1 AMK DNS",
            "nameserver 10.244.88.8",
            "nameserver 10.244.88.9"
        ],
        "devhealthgrp.com.sg": [
            "search devhealthgrp.com.sg",
            "# HDC2 FORT DNS",
            "nameserver 10.246.168.19",
            "# HDC1 AMK DNS",
            "nameserver 10.246.40.19"
        ],
        "healthgrpextp.com.sg": [
            "search healthgrpextp.com.sg",
            "# HDC2 FORT DNS",
            "nameserver 10.243.151.8",
            "nameserver 10.243.151.9",
            "# HDC1 AMK DNS",
            "nameserver 10.239.151.8",
            "nameserver 10.239.151.9"
        ],
        "hcloud.healthgrp.com.sg": [
            "search hcloud.healthgrp.com.sg",
            "# HDC2 FORT DNS",
            "nameserver 10.245.152.18",
            "nameserver 10.245.152.19",
            "# HDC1 AMK DNS",
            "nameserver 10.244.152.18",
            "nameserver 10.244.152.19"
        ],
        "iltc.healthgrp.com.sg": [
            "search iltc.healthgrp.com.sg",
            "# HDC2 FORT DNS",
            "nameserver 10.240.152.19",
            "nameserver 10.240.152.20",
            "# HDC1 AMK DNS (Nil)"
        ],
        "nhg.local": [
            "search nhg.local",
            "# HDC2 FORT DNS",
            "nameserver 10.240.152.36",
            "nameserver 10.240.152.37",
            "# HDC1 AMK DNS",
            "nameserver 10.237.152.30",
            "nameserver 10.237.152.31"
        ],
        "aic.local": [
            "search aic.local",
            "# HDC2 FORT DNS",
            "nameserver 10.240.152.18",
            "nameserver 10.240.152.31",
            "# HDC1 AMK DNS (Nil)"
        ]
    }
    
    if domain in hdc1_entries and domain in hdc2_entries:
        if dc_location.lower() == 'hdc1':
            return hdc1_entries[domain]
        elif dc_location.lower() == 'hdc2':
            return hdc2_entries[domain]
        else:
            print(f"Invalid DC_Location: {dc_location}")
            sys.exit(1)
    else:
        print(f"Domain not found: {domain}")
        sys.exit(1)

def update_resolv_conf(fqdn, dc_location):
    domain = get_domain_from_fqdn(fqdn)
    resolv_conf_path = '/etc/resolv.conf'
    new_entries = get_new_entries(domain, dc_location)

    try:
        with open(resolv_conf_path, 'r') as file:
            lines = file.readlines()

        current_entries = [line.strip() for line in lines if line.startswith('search') or line.startswith('nameserver') or line.startswith('#')]
        
        if current_entries == new_entries:
            print(f"No changes needed for {fqdn} with {dc_location}")
            return

        with open(resolv_conf_path, 'w') as file:
            for line in lines:
                if line.startswith('nameserver') or line.startswith('search') or line.startswith('#'):
                    continue
                file.write(line)
            for entry in new_entries:
                file.write(entry + '\n')

        print(f"{resolv_conf_path} updated successfully for {fqdn} with {dc_location}")

    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python script.py <FQDN> <HDC1|HDC2>")
        sys.exit(1)

    fqdn = sys.argv[1]
    dc_location = sys.argv[2]
    update_resolv_conf(fqdn, dc_location)
