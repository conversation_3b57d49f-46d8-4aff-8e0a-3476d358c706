# Simple File Extension Renamer

A lightweight batch script for renaming file extensions to .txt and back without requiring admin rights or PowerShell.

**Author:** <PERSON> - CES Operational Excellence Team

## Features

- Simple text-based menu interface
- Convert file extensions to .txt for air-gapped environments
- Restore original file extensions from .txt
- Option to process subfolders
- File/folder selection interface with checkboxes
- Automatic selection of all child objects when a folder is selected
- System compatibility testing to identify potential issues
- Enhanced error handling and path validation
- No admin rights required
- No PowerShell dependency
- Detailed operation logging
- Uses temporary directory for logs and map files to avoid permission issues

## Requirements

- Windows operating system
- No special permissions needed

## How to Use

1. Double-click the `SimpleExtensionRenamer.bat` file
2. From the menu, select:
   - Option 1: Convert file extensions to .txt
   - Option 2: Restore original file extensions
   - Option 3: Test system compatibility
   - Option 4: Exit

### Testing System Compatibility

1. Select option 3 from the menu
2. The script will run a series of tests to check:
   - If the TEMP directory is accessible
   - If the current directory is writable
   - If Windows Script Host (cscript) is available
   - If VBScript execution is working
   - If the folder browser dialog can be displayed
3. Review the test results to identify any potential issues
4. If any tests fail, the script will try to use alternative methods when possible

### Converting Extensions to .txt

1. Select option 1 from the menu
2. A folder browser dialog will appear - select the folder containing files to convert
3. Choose whether to process subfolders (Y/N)
4. A file selection interface will appear showing all files and folders in a tree view
   - Select the specific files and folders you want to process
   - Selecting a folder automatically selects all files within it
   - Use the "Select All" and "Deselect All" buttons for quick selection
5. Click "OK" to proceed with the selected files
6. The script will:
   - Convert all selected file extensions to .txt
   - Create extension map files in both your temp directory and the selected folder
   - Log all operations to a timestamped log file in your temp directory

### Restoring Original Extensions

1. Select option 2 from the menu
2. A folder browser dialog will appear - select the folder containing files to restore
3. If the extension map file is not found in the selected folder:
   - You'll be prompted to enter the timestamp from the conversion process
   - Or you can leave it blank to search for any available map file
4. A file selection interface will appear showing all files organized by folder
   - Select the specific files you want to restore
   - Selecting a folder automatically selects all files within it
   - Use the "Select All" and "Deselect All" buttons for quick selection
5. Click "OK" to proceed with the selected files
6. The script will:
   - Read the extension map file
   - Rename only the selected .txt files back to their original extensions
   - Log all operations to a timestamped log file in your temp directory
   - Remove the map file if all files were successfully restored

## How It Works

### Convert Mode

1. Creates text files that record the original extension of each file:
   - Primary map file in your temp directory (`%TEMP%\extension_map_[timestamp].txt`)
   - Backup copy in the selected folder (`extension_map.txt`)
2. Renames selected files to have a `.txt` extension
3. Logs all operations to a log file in your temp directory

### Restore Mode

1. Looks for the extension map file in the selected folder
2. If not found, searches in your temp directory or asks for the timestamp
3. Reads the extension map file to identify original file extensions
4. Renames `.txt` files back to their original extensions
5. Logs all operations to a log file in your temp directory
6. Removes the map file after successful restoration

## Notes

- Files that already have a `.txt` extension are skipped during conversion
- The tool will not process the extension map file itself
- Log files are stored in your temp directory (`%TEMP%\ExtensionRenamer_logs`)
- Extension map files are created in both your temp directory and the selected folder
- The script uses your temp directory to avoid permission issues on restricted systems

## Troubleshooting

If you encounter any issues:

1. Run the system compatibility test (Option 3) to identify potential problems
2. Check the log files in your temp directory (`%TEMP%\ExtensionRenamer_logs`) for detailed information
3. Ensure you have write permissions in the selected folder
4. If you get "The system cannot find the specified path" error:
   - Make sure the selected folder exists and is accessible
   - Try running the script from a different location
   - The script will attempt to use the current directory if the temp directory is not accessible
5. If restoring extensions fails:
   - Check if the extension map file exists in the selected folder
   - If not, the script will try to find it in your temp directory
   - You can also manually copy the map file from your temp directory to the folder
6. For large directories, be patient as processing may take some time
7. If you get "Access is denied" errors, the script is designed to use your temp directory which should be writable
