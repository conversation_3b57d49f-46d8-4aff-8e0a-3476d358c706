#!/usr/bin/env python3
# Enhanced NetBackup bp.conf configuration script
# Version 2.0 - Improved error handling, validation, and security

import subprocess
import sys
import os
import json
from datetime import datetime

def log_message(message, level="INFO"):
    """Log messages with timestamp and level"""
    timestamp = datetime.now().isoformat()
    print(f"[{timestamp}] {level}: {message}")

def get_hostname():
    """Get system hostname safely"""
    try:
        result = subprocess.run(['hostnamectl', '--static'], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE,
                              universal_newlines=True,
                              timeout=30)
        
        if result.returncode == 0:
            hostname = result.stdout.strip()
            # Return short hostname (first part before first dot)
            return hostname.split('.')[0]
        else:
            log_message(f"Failed to get hostname: {result.stderr}", "ERROR")
            return None
            
    except subprocess.TimeoutExpired:
        log_message("Timeout getting hostname", "ERROR")
        return None
    except Exception as e:
        log_message(f"Exception getting hostname: {e}", "ERROR")
        return None

def load_netbackup_configuration():
    """Load NetBackup server configuration"""
    return {
        "required_servers": [
            # Domain 2C servers
            "SERVER = HISBKPVPMAS30",
            "SERVER = HISBKPPPMAS16", 
            "SERVER = HISBKPPPMAS66",
            "SERVER = HISBKPPPMED36",
            "SERVER = HISBKPPPMED37",
            "SERVER = HISBKPPPMED38",
            "SERVER = HISBKPPPMED39",
            "SERVER = HISBKPPPMED66",
            "SERVER = HISBKPPPMED67",
            "SERVER = HISBKPPPMED68",
            "SERVER = HISBKPPPMED69",
            # Domain 2B servers
            "SERVER = HISBKPVPMAS20",
            "SERVER = HISBKPPPMAS15",
            "SERVER = HISBKPPPMAS65",
            "SERVER = HISBKPPPMED07",
            "SERVER = HISBKPPPMED31",
            "SERVER = HISBKPPPMED32",
            "SERVER = HISBKPPPMED33",
            "SERVER = HISBKPPPMED34",
            "SERVER = HISBKPPPMED35",
            "SERVER = HISBKPPPAPL07",
            "SERVER = HISBKPPPMED57",
            "SERVER = HISBKPPPMED61",
            "SERVER = HISBKPPPMED62",
            "SERVER = HISBKPPPMED63",
            "SERVER = HISBKPPPMED64",
            "SERVER = HISBKPPPMED65",
            "SERVER = HISBKPPPAPL57",
            # Domain 2 servers
            "SERVER = HISBKPVPMAS10",
            "SERVER = HISBKPPPMAS12",
            "SERVER = HISBKPVPMAS13",
            "SERVER = HISBKPPPMAS62",
            "SERVER = HISBKPVPMAS63",
            "SERVER = hisbkpppapl12",
            "SERVER = hisbkpppapl13",
            "SERVER = hisbkpppapl15",
            "SERVER = hisbkpppapl16",
            "SERVER = hisbkpppapl62",
            "SERVER = hisbkpppapl63",
            "SERVER = hisbkpppapl65",
            "SERVER = hisbkpppapl66",
            "SERVER = HISBKPPPAPL67"
        ],
        "connect_options": "CONNECT_OPTIONS = localhost 1 0 2"
    }

def validate_hostname(hostname):
    """Validate hostname format"""
    if not hostname:
        return False
    
    # Basic hostname validation
    if len(hostname) > 63 or len(hostname) < 1:
        return False
    
    # Check for valid characters
    import re
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$'
    return re.match(pattern, hostname) is not None

def backup_bp_conf(file_path):
    """Create backup of existing bp.conf file"""
    try:
        if os.path.exists(file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{file_path}.backup.{timestamp}"
            
            subprocess.run(['cp', file_path, backup_path], 
                         check=True, 
                         timeout=30)
            log_message(f"Backed up existing bp.conf to {backup_path}")
            return backup_path
        else:
            log_message("No existing bp.conf file to backup")
            return None
            
    except Exception as e:
        log_message(f"Failed to backup bp.conf: {e}", "ERROR")
        return None

def create_directory_if_needed(directory):
    """Create NetBackup directory if it doesn't exist"""
    try:
        if not os.path.exists(directory):
            os.makedirs(directory, mode=0o755, exist_ok=True)
            log_message(f"Created directory: {directory}")
            return True
        else:
            log_message(f"Directory already exists: {directory}")
            return True
            
    except Exception as e:
        log_message(f"Failed to create directory {directory}: {e}", "ERROR")
        return False

def read_existing_bp_conf(file_path):
    """Read existing bp.conf file and return non-server/client lines"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as file:
                lines = file.readlines()
            
            # Filter out SERVER and CLIENT_NAME lines, keep others
            filtered_lines = []
            for line in lines:
                line = line.strip()
                if (not line.startswith("SERVER =") and 
                    not line.startswith("CLIENT_NAME =") and
                    not line.startswith("CONNECT_OPTIONS")):
                    filtered_lines.append(line)
            
            return filtered_lines
        else:
            return []
            
    except Exception as e:
        log_message(f"Failed to read existing bp.conf: {e}", "ERROR")
        return []

def write_bp_conf(file_path, hostname):
    """Write new bp.conf file with proper configuration"""
    try:
        netbackup_config = load_netbackup_configuration()
        
        # Read existing configuration (excluding server/client entries)
        existing_lines = read_existing_bp_conf(file_path)
        
        # Build new configuration
        new_lines = []
        
        # Add existing non-server/client lines
        for line in existing_lines:
            if line.strip():  # Skip empty lines for now
                new_lines.append(line)
        
        # Add required servers
        for server in netbackup_config["required_servers"]:
            new_lines.append(server)
        
        # Add client name
        client_name_line = f'CLIENT_NAME = {hostname}'
        new_lines.append(client_name_line)
        
        # Add connect options
        new_lines.append(netbackup_config["connect_options"])
        
        # Write to temporary file first
        temp_file = f"{file_path}.tmp"
        with open(temp_file, 'w') as file:
            for line in new_lines:
                file.write(line + '\n')
        
        # Validate temporary file
        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
            # Move to final location
            subprocess.run(['mv', temp_file, file_path], 
                         check=True, 
                         timeout=30)
            subprocess.run(['chmod', '644', file_path], 
                         check=True, 
                         timeout=30)
            log_message(f"Successfully updated bp.conf with client name: {hostname}")
            return True
        else:
            log_message("Temporary bp.conf file validation failed", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"Failed to write bp.conf: {e}", "ERROR")
        return False

def verify_bp_conf(file_path, hostname):
    """Verify bp.conf file was created correctly"""
    try:
        if not os.path.exists(file_path):
            log_message("bp.conf file does not exist after creation", "ERROR")
            return False
        
        with open(file_path, 'r') as file:
            content = file.read()
        
        # Check for required elements
        if f"CLIENT_NAME = {hostname}" not in content:
            log_message(f"CLIENT_NAME = {hostname} not found in bp.conf", "ERROR")
            return False
        
        if "CONNECT_OPTIONS = localhost 1 0 2" not in content:
            log_message("CONNECT_OPTIONS not found in bp.conf", "ERROR")
            return False
        
        # Count server entries
        server_count = content.count("SERVER =")
        if server_count == 0:
            log_message("No SERVER entries found in bp.conf", "ERROR")
            return False
        
        log_message(f"bp.conf verification successful: {server_count} servers configured")
        return True
        
    except Exception as e:
        log_message(f"Failed to verify bp.conf: {e}", "ERROR")
        return False

def main():
    """Main function with enhanced error handling"""
    bp_conf_path = '/usr/openv/netbackup/bp.conf'
    netbackup_dir = '/usr/openv/netbackup'
    
    log_message("Starting NetBackup bp.conf configuration")
    
    # Get hostname
    hostname = get_hostname()
    if not hostname:
        log_message("Failed to get system hostname", "ERROR")
        sys.exit(1)
    
    # Validate hostname
    if not validate_hostname(hostname):
        log_message(f"Invalid hostname format: {hostname}", "ERROR")
        sys.exit(1)
    
    log_message(f"Configuring bp.conf for hostname: {hostname}")
    
    # Create directory if needed
    if not create_directory_if_needed(netbackup_dir):
        log_message("Failed to create NetBackup directory", "ERROR")
        sys.exit(1)
    
    # Backup existing file
    backup_bp_conf(bp_conf_path)
    
    # Write new configuration
    if not write_bp_conf(bp_conf_path, hostname):
        log_message("Failed to write bp.conf", "ERROR")
        sys.exit(1)
    
    # Verify configuration
    if not verify_bp_conf(bp_conf_path, hostname):
        log_message("bp.conf verification failed", "ERROR")
        sys.exit(1)
    
    log_message("NetBackup bp.conf configuration completed successfully")
    sys.exit(0)

if __name__ == "__main__":
    main()
