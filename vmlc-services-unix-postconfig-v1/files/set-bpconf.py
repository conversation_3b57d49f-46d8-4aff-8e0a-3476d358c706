#!/usr/bin/env python3

import subprocess
import os
import sys

required_servers = [
    "SERVER = hisbkpvpmas30",
    "SERVER = HISBKPPPMAS16",
    "SERVER = HISBKPPPMAS66",
    "SERVER = hisbkpvpmas20",
    "SERVER = HISBKPPPMAS15",
    "SERVER = HISBKPPPMAS65",
    "SERVER = hisbkpvpmas10",
    "SERVER = hisbkpppmas12",
    "SERVER = hisbkpppmas62",
    "SERVER = hisbkpppmed31",
    "SERVER = hisbkpppmed32",
    "SERVER = hisbkpppmed33",
    "SERVER = hisbkpppmed34",
    "SERVER = hisbkpppmed35",
    "SERVER = hisbkpppmed36",
    "SERVER = hisbkpppmed37",
    "SERVER = hisbkpppmed38",
    "SERVER = hisbkpppmed39",
    "SERVER = hisbkpppmed61",
    "SERVER = hisbkpppmed62",
    "SERVER = hisbkpppmed63",
    "SERVER = hisbkpppmed64",
    "SERVER = hisbkpppmed65",
    "SERVER = hisbkpppmed66",
    "SERVER = hisbkpppmed67",
    "SERVER = hisbkpppmed68",
    "SERVER = hisbkpppmed69",
    "SERVER = hisbkpppapl12",
    "SERVER = hisbkpppapl13",
    "SERVER = hisbkpppapl15",
    "SERVER = hisbkpppapl16",
    "SERVER = hisbkpppapl62",
    "SERVER = hisbkpppapl63",
    "SERVER = hisbkpppapl65",
    "SERVER = hisbkpppapl66",
    "SERVER = hisbkpppapl67",
    "SERVER = HISBKPPPAPL07",
    "SERVER = HISBKPPPAPL57"
]

def get_hostname():
    result = subprocess.run(['hostnamectl', '--static'], stdout=subprocess.PIPE, universal_newlines=True)
    hostname = result.stdout.strip()
    hostname = hostname.split('.')[0]
    return hostname

def update_bp_conf(file_path, hostname):
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        print(f"Error: Directory {directory} does not exist.")
        sys.exit(1)
    
    if os.path.exists(file_path):
        with open(file_path, 'r') as file:
            lines = file.readlines()
    else:
        lines = []

    lines = [line for line in lines if not line.startswith("SERVER =") and not line.startswith("CLIENT_NAME =")]

    for server in required_servers:
        lines.append(server + '\n')

    client_name_line = f'CLIENT_NAME = {hostname}\n'
    lines.append(client_name_line)

    lines = [line for line in lines if not line.startswith('CONNECT_OPTIONS')]
    lines.append("CONNECT_OPTIONS = localhost 1 0 2\n")

    with open(file_path, 'w') as file:
        file.writelines(lines)

if __name__ == "__main__":
    bp_conf_path = '/usr/openv/netbackup/bp.conf'
    hostname = get_hostname()
    update_bp_conf(bp_conf_path, hostname)
