---
# =========================================================================
# Main tasks entry point for DNS management
# =========================================================================
# This file serves as the main entry point for DNS management tasks
# It includes the dns.yml file which contains all the DNS-related operations
# The 'dns' tag allows for selective execution of DNS tasks
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

- name: Trigger DNS Tasks
  ansible.builtin.include_tasks: dns.yml
  tags: dns