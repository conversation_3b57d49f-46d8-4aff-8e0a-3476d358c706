# File Extension Tool - Technical Documentation

## Author

**<PERSON>**  
CES Operational Excellence Team

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Component Details](#component-details)
3. [Data Structures](#data-structures)
4. [Algorithms](#algorithms)
5. [File Format](#file-format)
6. [Security Considerations](#security-considerations)
7. [Performance Optimization](#performance-optimization)
8. [Error Handling](#error-handling)
9. [Testing](#testing)
10. [Build Process](#build-process)
11. [Future Enhancements](#future-enhancements)

## Architecture Overview

The File Extension Tool is built using Python with a Tkinter GUI, packaged as standalone executables using PyInstaller. The architecture follows a modular design with separate components for different functionalities:

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  GUI Layer      │────▶│  Business Logic │────▶│  File System    │
│  (Tkinter)      │     │  Layer          │     │  Operations     │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                      │                       │
         │                      │                       │
         ▼                      ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Configuration  │     │  Extension      │     │  Logging &      │
│  Management     │     │  Mapping        │     │  Error Handling │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Key Components

1. **Simple File Renamer**: Handles individual file renaming operations
2. **Batch File Renamer**: Processes multiple files and directories
3. **Version Management**: Tracks and manages file versions
4. **Extension Mapper**: Maps between original and .txt extensions
5. **Configuration Manager**: Handles user preferences and settings
6. **Logging System**: Records operations and errors

## Component Details

### Simple File Renamer

The Simple File Renamer provides a straightforward interface for renaming individual files:

- **User Interface**: File selection dialog and operation buttons
- **Core Functions**:
  - `rename_to_txt(file_path)`: Renames a file to .txt extension
  - `restore_extension(file_path)`: Restores original extension
  - `create_backup(file_path)`: Creates a backup before renaming

### Batch File Renamer

The Batch File Renamer processes multiple files and directories:

- **User Interface**: Directory selection, options panel, progress display
- **Core Functions**:
  - `scan_directory(dir_path, recursive)`: Scans for files to process
  - `filter_files(file_list, patterns)`: Filters files based on patterns
  - `batch_rename(file_list, mode)`: Processes multiple files
  - `generate_report(results)`: Creates operation summary

### Version Management

The Version Management component tracks different versions of renamed files:

- **User Interface**: Version list, comparison view, restore options
- **Core Functions**:
  - `create_version(dir_path, name, description)`: Creates a version snapshot
  - `restore_version(version_id, target_dir)`: Restores a version
  - `compare_versions(version1, version2)`: Compares two versions
  - `export_comparison(comparison_data)`: Exports comparison results

### Extension Mapper

The Extension Mapper handles the mapping between original and .txt extensions:

- **Data Structure**: JSON-based mapping database
- **Core Functions**:
  - `save_original_extension(file_path)`: Records original extension
  - `get_original_extension(file_path)`: Retrieves original extension
  - `update_mapping_database(mapping_data)`: Updates the mapping database

### Configuration Manager

The Configuration Manager handles user preferences and settings:

- **Data Structure**: JSON configuration file
- **Core Functions**:
  - `load_config()`: Loads user configuration
  - `save_config(config_data)`: Saves user configuration
  - `reset_to_defaults()`: Resets configuration to defaults

### Logging System

The Logging System records operations and errors:

- **Log Format**: Timestamped entries with severity levels
- **Core Functions**:
  - `log_operation(operation, file_path, result)`: Logs an operation
  - `log_error(error_message, file_path)`: Logs an error
  - `export_logs(start_date, end_date)`: Exports logs for a date range

## Data Structures

### Extension Mapping Database

The extension mapping is stored in a JSON file with the following structure:

```json
{
  "mapping_version": "1.0",
  "last_updated": "2023-04-15T10:30:00",
  "entries": [
    {
      "original_path": "C:\\Project\\script.py",
      "renamed_path": "C:\\Project\\script.py.txt",
      "original_extension": ".py",
      "timestamp": "2023-04-15T10:25:00"
    },
    {
      "original_path": "C:\\Project\\config.json",
      "renamed_path": "C:\\Project\\config.json.txt",
      "original_extension": ".json",
      "timestamp": "2023-04-15T10:25:05"
    }
  ]
}
```

### Version Database

The version database is stored in a SQLite database with the following schema:

```sql
CREATE TABLE versions (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_timestamp TEXT NOT NULL,
    base_directory TEXT NOT NULL
);

CREATE TABLE version_files (
    id INTEGER PRIMARY KEY,
    version_id INTEGER,
    original_path TEXT NOT NULL,
    relative_path TEXT NOT NULL,
    file_hash TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    FOREIGN KEY (version_id) REFERENCES versions (id)
);
```

### Configuration File

The configuration is stored in a JSON file with the following structure:

```json
{
  "general": {
    "create_backups": true,
    "backup_directory": "./backups",
    "preserve_timestamps": true,
    "log_operations": true,
    "log_level": "INFO"
  },
  "batch_renamer": {
    "process_subfolders": true,
    "excluded_extensions": [".exe", ".dll"],
    "excluded_directories": ["node_modules", "__pycache__"]
  },
  "version_management": {
    "database_path": "./versions.db",
    "auto_snapshot": false,
    "max_versions": 10
  },
  "ui": {
    "theme": "system",
    "font_size": 10,
    "show_tooltips": true
  }
}
```

## Algorithms

### File Renaming Algorithm

```
function rename_file(file_path, mode):
    if mode == "to_txt":
        if file_path already ends with .txt:
            return error("File already has .txt extension")
        
        original_extension = get_file_extension(file_path)
        new_path = file_path + ".txt"
        
        if create_backups is enabled:
            create_backup(file_path)
        
        rename file from file_path to new_path
        save_mapping(file_path, new_path, original_extension)
        
        return success(new_path)
    
    else if mode == "restore":
        if file_path does not end with .txt:
            return error("File does not have .txt extension")
        
        original_extension = get_original_extension(file_path)
        if original_extension is None:
            return error("Original extension not found in mapping")
        
        new_path = remove_txt_extension(file_path)
        
        if create_backups is enabled:
            create_backup(file_path)
        
        rename file from file_path to new_path
        update_mapping(file_path, new_path)
        
        return success(new_path)
```

### Directory Scanning Algorithm

```
function scan_directory(dir_path, recursive, exclusions):
    file_list = empty list
    
    for each entry in dir_path:
        if entry is a directory and recursive is true:
            if entry name is not in exclusions:
                sub_files = scan_directory(entry, recursive, exclusions)
                add sub_files to file_list
        
        else if entry is a file:
            if file extension is not in exclusions:
                add entry to file_list
    
    return file_list
```

### Version Comparison Algorithm

```
function compare_versions(version1_id, version2_id):
    version1_files = get_files_for_version(version1_id)
    version2_files = get_files_for_version(version2_id)
    
    added_files = files in version2 but not in version1
    removed_files = files in version1 but not in version2
    
    modified_files = empty list
    for each file in version1 that also exists in version2:
        if file_hash in version1 != file_hash in version2:
            add file to modified_files
    
    unchanged_files = files in version1 that also exist in version2 with same hash
    
    return {
        "added": added_files,
        "removed": removed_files,
        "modified": modified_files,
        "unchanged": unchanged_files
    }
```

## File Format

### Mapping File

The mapping file (`extension_mapping.json`) stores the relationship between original files and their .txt versions:

- **Format**: JSON
- **Location**: Application directory
- **Backup**: Automatic backup created before modifications
- **Versioning**: Includes a version number for format changes

### Version Database

The version database (`versions.db`) stores information about file versions:

- **Format**: SQLite database
- **Location**: User-configurable (default: application directory)
- **Backup**: Automatic backup created daily
- **Compression**: Database is compacted periodically

### Log Files

Log files record all operations and errors:

- **Format**: Plain text with timestamp and severity
- **Location**: `./logs` directory
- **Rotation**: Daily log files with date-based naming
- **Retention**: Configurable retention period (default: 30 days)

## Security Considerations

### File Permissions

- The tool requires read/write access to files being processed
- Administrator privileges may be required for system directories
- The tool preserves original file permissions when possible

### Data Protection

- Original file content is never modified, only extensions
- Backups are created before operations when enabled
- Mapping database is protected against corruption with transaction support

### Error Prevention

- Validation checks prevent operations that could cause data loss
- Confirmation dialogs for potentially destructive operations
- Automatic rollback if an operation fails mid-process

## Performance Optimization

### Batch Processing

- Files are processed in batches to optimize memory usage
- Progress reporting is throttled to reduce UI updates
- Background worker threads handle file operations

### Memory Management

- Large directory scans use generators to reduce memory footprint
- File content is never loaded into memory
- Database queries use indexes for efficient lookups

### Caching

- Recently accessed mapping entries are cached
- Directory structure is cached during batch operations
- Configuration is loaded once and cached

## Error Handling

### Error Types

1. **File System Errors**:
   - Permission denied
   - File not found
   - Path too long
   - Disk full

2. **Mapping Errors**:
   - Mapping not found
   - Corrupted mapping file
   - Duplicate mapping entries

3. **Version Database Errors**:
   - Database locked
   - Corrupted database
   - Version not found

### Recovery Mechanisms

- **Transaction Rollback**: Database operations use transactions
- **Automatic Repair**: Corrupted mapping files are repaired when possible
- **Backup Restoration**: Critical files can be restored from backups
- **Graceful Degradation**: Non-critical features are disabled on error

## Testing

### Unit Tests

Unit tests cover core functionality:

- File renaming operations
- Extension mapping
- Directory scanning
- Version management

### Integration Tests

Integration tests verify component interaction:

- GUI to business logic
- Business logic to file system
- Configuration to components

### User Acceptance Testing

UAT scenarios include:

- Simple file renaming
- Batch processing of directories
- Version creation and restoration
- Error handling and recovery

## Build Process

### Development Environment

- **Language**: Python 3.8+
- **GUI Framework**: Tkinter
- **Database**: SQLite
- **Packaging**: PyInstaller

### Build Steps

1. **Prepare Environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # or venv\Scripts\activate on Windows
   pip install -r requirements.txt
   ```

2. **Run Tests**:
   ```bash
   pytest tests/
   ```

3. **Build Executables**:
   ```bash
   pyinstaller --onefile --windowed SimpleFileRenamer.py
   pyinstaller --onefile --windowed BatchFileRenamer.py
   pyinstaller --onefile --windowed SimpleVersions.py
   ```

4. **Package Release**:
   ```bash
   python scripts/package_release.py
   ```

### Continuous Integration

The build process is automated using GitHub Actions:

- **Trigger**: Push to main branch or pull request
- **Steps**: Lint, test, build, package
- **Artifacts**: Executable files and documentation

## Future Enhancements

Planned enhancements include:

1. **Enhanced GUI**:
   - Dark mode support
   - Customizable themes
   - Improved accessibility

2. **Advanced Filtering**:
   - Content-based filtering
   - Regular expression support
   - Smart detection of file types

3. **Cloud Integration**:
   - OneDrive/SharePoint synchronization
   - Version storage in cloud services
   - Cross-device synchronization

4. **Reporting**:
   - Detailed operation reports
   - Visual file difference viewer
   - Export to multiple formats

5. **Automation**:
   - Scheduled operations
   - Watched folders
   - Integration with CI/CD pipelines
