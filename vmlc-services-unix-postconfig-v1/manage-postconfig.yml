# VM Post-Configuration Playbook for RHEL/Oracle Linux - Version 2
# Enhanced with Ansible best practices, proper error handling, and role-based structure
# Configures hostname, PAM accounts, backup, time sync, and DNS resolution
# Author: CES Operational Excellence Team
# Version: 2.0

- name: VM Post-Configuration Tasks for Redhat and Oracle Linux
  hosts: all
  become: true
  gather_facts: true

  vars:
    # Convert conflicting variable names to use var_ prefix
    var_fqdn: "{{ FQDN | default('') }}"
    var_prd_ip: "{{ PRD_IP | default('') }}"
    var_environment: "{{ Environment | default('production') }}"
    var_pam_action: "{{ pam_action | default('add') }}"
    var_dc_location: "{{ dc_location | default('HDC2') }}"
    var_debug_output: "{{ debug_output | default(false) }}"
    var_ignore_errors: "{{ ignore_errors | default(false) }}"

    # Logging configuration
    log_file: "/var/log/ansible-postconfig.log"

  pre_tasks:
    - name: Validate required variables
      ansible.builtin.assert:
        that:
          - var_fqdn != ""
          - var_prd_ip != ""
          - var_dc_location in ['HDC1', 'HDC2']
          - var_environment in ['production', 'staging', 'development']
        fail_msg: "Required variables missing or invalid. Check FQDN, PRD_IP, dc_location, and Environment."
        success_msg: "All required variables validated successfully."

    - name: Initialize logging
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Starting VM Post-Configuration for {{ var_fqdn }}"
        create: true
        mode: '0644'
      delegate_to: localhost
      run_once: true

    - name: Processing New VM AAP Inventory, Instance Group and Credentials selection
      ansible.builtin.command: ./files/set-aapreq.py
      delegate_to: localhost
      register: aap_result
      failed_when: false
      changed_when: aap_result.rc == 0

  roles:
    - role: unix-postconfig
      vars:
        fqdn: "{{ var_fqdn }}"
        prd_ip: "{{ var_prd_ip }}"
        environment: "{{ var_environment }}"
        pam_action: "{{ var_pam_action }}"
        dc_location: "{{ var_dc_location }}"
        debug_output: "{{ var_debug_output }}"
        ignore_configuration_errors: "{{ var_ignore_errors }}"

  post_tasks:
    - name: Final logging
      ansible.builtin.lineinfile:
        path: "{{ log_file }}"
        line: "{{ ansible_date_time.iso8601 }} - Completed VM Post-Configuration for {{ var_fqdn }}"
      delegate_to: localhost
      run_once: true