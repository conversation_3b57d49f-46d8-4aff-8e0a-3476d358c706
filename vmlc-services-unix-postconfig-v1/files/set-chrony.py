#!/usr/bin/env python3

import argparse

ntp_servers = {
    'HDC1': [
        '##HDC1 AMK NTP servers\n',
        'server *********** iburst\n',
        'server *********** iburst\n',
        '##HDC2 FORT NTP servers\n',
        'server ************ iburst\n',
        'server ************ iburst\n',
    ],
    'HDC2': [
        '##HDC2 FORT NTP servers\n',
        'server ************ iburst\n',
        'server ************ iburst\n',
        '##HDC1 AMK NTP servers\n',
        'server *********** iburst\n',
        'server *********** iburst\n',
    ]
}

def replace_lines(file_path, section):
    in_ntp_section = False
    section_found = False
    output_lines = []
    ntp_lines_to_add = ntp_servers[section]
    lines_to_remove = set([line.strip() for key in ntp_servers for line in ntp_servers[key]])

    with open(file_path, 'r') as file:
        for line in file:
            stripped_line = line.strip()
            if stripped_line in lines_to_remove:
                continue
            if line.startswith('##HDC1') or line.startswith('##HDC2'):
                in_ntp_section = True
                if section in line:
                    section_found = True
                continue
            if in_ntp_section and line.startswith('server'):
                continue
            if in_ntp_section and not line.startswith('server'):
                in_ntp_section = False

            output_lines.append(line)

    if not section_found:
        append_position = -1
        for i, line in enumerate(output_lines):
            if '# Please consider joining the pool (http://www.pool.ntp.org/join.html).' in line:
                append_position = i + 1
                break
        if append_position != -1:
            output_lines[append_position:append_position] = ntp_lines_to_add
        else:
            output_lines.extend(ntp_lines_to_add)
    else:
        for ntp_line in ntp_lines_to_add:
            if ntp_line not in output_lines:
                output_lines.append(ntp_line)

    with open(file_path, 'w') as file:
        file.writelines(output_lines)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Update chrony.conf with specified NTP servers.')
    parser.add_argument('section', choices=['HDC1', 'HDC2'], help='Section to update (HDC1 or HDC2)')
    args = parser.parse_args()

    file_path = "/etc/chrony.conf"
    replace_lines(file_path, args.section)
