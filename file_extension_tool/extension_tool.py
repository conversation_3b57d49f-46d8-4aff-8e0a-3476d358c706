#!/usr/bin/env python3
"""
File Extension Tool for Air Gap Solutions

This tool allows users to:
1. Convert all file extensions in a directory to .txt (Prep mode)
2. Restore all file extensions to their original form (Restore mode)

Author: <PERSON>
"""

import os
import json
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import logging
from datetime import datetime
import sys
import traceback
import shutil
import zipfile
from typing import Dict, List, Set, Tuple, Optional

# Set up logging
log_directory = "logs"
os.makedirs(log_directory, exist_ok=True)
log_filename = os.path.join(log_directory, f"extension_tool_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)

class FileExtensionTool:
    def __init__(self, root):
        self.root = root
        self.root.title("File Extension Tool for Air Gap Solutions")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Create a custom style for the progress bar
        style = ttk.Style()
        style.configure("Thick.Horizontal.TProgressbar", thickness=25)

        # Set icon if available
        try:
            # For Windows
            if os.name == 'nt':
                self.root.iconbitmap("icon.ico")
            # For Linux/Unix
            else:
                logo = tk.PhotoImage(file="icon.png")
                self.root.iconphoto(True, logo)
        except Exception as e:
            logging.debug(f"Could not set icon: {str(e)}")

        self.setup_ui()

        # Store for original extensions
        self.extension_map_file = "extension_map.json"

        # Files to ignore
        self.ignore_files = [
            self.extension_map_file,
            os.path.basename(__file__),
            "icon.ico",
            "icon.png",
            # Removed README.md from ignore list to allow processing in subdirectories
            "requirements.txt",
            "build.bat",
            "build.sh"
        ]

        # Extensions to ignore
        self.ignore_extensions = [".txt", ".log", ".json", ".exe", ".spec"]

    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="File Extension Tool for Air Gap Solutions",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Directory selection
        dir_frame = ttk.Frame(main_frame)
        dir_frame.pack(fill=tk.X, pady=10)

        ttk.Label(dir_frame, text="Directory:").pack(side=tk.LEFT, padx=(0, 10))

        self.dir_var = tk.StringVar()
        dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, width=50)
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        browse_btn = ttk.Button(dir_frame, text="Browse", command=self.browse_directory)
        browse_btn.pack(side=tk.LEFT)

        # Scan button to populate file list
        scan_btn = ttk.Button(dir_frame, text="Scan Directory", command=self.scan_directory)
        scan_btn.pack(side=tk.LEFT, padx=(5, 0))

        # File selection frame
        selection_frame = ttk.LabelFrame(main_frame, text="Select Files/Folders to Process")
        selection_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Selection buttons
        select_btn_frame = ttk.Frame(selection_frame)
        select_btn_frame.pack(fill=tk.X, pady=5, padx=5)

        select_all_btn = ttk.Button(
            select_btn_frame,
            text="Select All",
            command=self.select_all_items,
            width=15
        )
        select_all_btn.pack(side=tk.LEFT, padx=5)

        deselect_all_btn = ttk.Button(
            select_btn_frame,
            text="Deselect All",
            command=self.deselect_all_items,
            width=15
        )
        deselect_all_btn.pack(side=tk.LEFT, padx=5)

        # Add Submit and Reset buttons
        submit_btn = ttk.Button(
            select_btn_frame,
            text="Submit Selection",
            command=self.submit_selection,
            width=15
        )
        submit_btn.pack(side=tk.LEFT, padx=5)

        reset_btn = ttk.Button(
            select_btn_frame,
            text="Reset",
            command=self.reset_selection,
            width=15
        )
        reset_btn.pack(side=tk.LEFT, padx=5)

        # File list with scrollbar
        file_list_frame = ttk.Frame(selection_frame)
        file_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Scrollbars
        y_scrollbar = ttk.Scrollbar(file_list_frame)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        x_scrollbar = ttk.Scrollbar(file_list_frame, orient=tk.HORIZONTAL)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Treeview for file list
        self.file_tree = ttk.Treeview(
            file_list_frame,
            columns=("path", "type"),
            show="tree headings",
            yscrollcommand=y_scrollbar.set,
            xscrollcommand=x_scrollbar.set,
            selectmode="extended"
        )
        self.file_tree.pack(fill=tk.BOTH, expand=True)

        # Configure scrollbars
        y_scrollbar.config(command=self.file_tree.yview)
        x_scrollbar.config(command=self.file_tree.xview)

        # Configure columns
        self.file_tree.column("#0", width=50, stretch=tk.NO)  # Checkbox column
        self.file_tree.column("path", width=400)
        self.file_tree.column("type", width=100)

        # Configure headings
        self.file_tree.heading("path", text="Path")
        self.file_tree.heading("type", text="Type")

        # Enable tree expansion/collapse on double-click
        self.file_tree.bind("<Double-1>", self.on_tree_double_click)

        # Store selected items
        self.selected_items = set()

        # Bind checkbox click
        self.file_tree.bind("<Button-1>", self.on_tree_click)

        # Action buttons
        btn_frame = ttk.LabelFrame(main_frame, text="Actions")
        btn_frame.pack(fill=tk.X, pady=10, padx=10)

        # Create two columns for buttons
        left_btn_frame = ttk.Frame(btn_frame)
        left_btn_frame.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5, pady=5)

        right_btn_frame = ttk.Frame(btn_frame)
        right_btn_frame.pack(side=tk.RIGHT, expand=True, fill=tk.X, padx=5, pady=5)

        # Left column buttons
        prep_copy_zip_btn = ttk.Button(
            left_btn_frame,
            text="Copy, Convert & Zip for Email",
            command=self.copy_convert_zip_mode,
            width=30
        )
        prep_copy_zip_btn.pack(pady=5, fill=tk.X)

        prep_btn = ttk.Button(
            left_btn_frame,
            text="Convert to .txt (In Place)",
            command=self.prep_mode,
            width=30
        )
        prep_btn.pack(pady=5, fill=tk.X)

        # Right column buttons
        restore_btn = ttk.Button(
            right_btn_frame,
            text="Restore Original Extensions",
            command=self.restore_mode,
            width=30
        )
        restore_btn.pack(pady=5, fill=tk.X)

        # Output folder selection
        output_frame = ttk.Frame(btn_frame)
        output_frame.pack(fill=tk.X, pady=5, padx=5)

        ttk.Label(output_frame, text="Output Directory:").pack(side=tk.LEFT, padx=(0, 10))

        self.output_dir_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var, width=50)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        output_browse_btn = ttk.Button(output_frame, text="Browse", command=self.browse_output_directory)
        output_browse_btn.pack(side=tk.LEFT)

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Status")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Add scrollbar to status
        scrollbar = ttk.Scrollbar(status_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.status_text = tk.Text(status_frame, height=10, yscrollcommand=scrollbar.set)
        self.status_text.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.status_text.yview)

        # Footer
        footer_frame = ttk.Frame(main_frame)
        footer_frame.pack(fill=tk.X, pady=(10, 0))

        author_label = ttk.Label(
            footer_frame,
            text="Created by: Muhammad Syazani Bin Mohamed Khairi - CES Operational Excellence Team",
            font=("Arial", 8)
        )
        author_label.pack(side=tk.LEFT)

        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="Progress")
        progress_frame.pack(fill=tk.X, pady=(10, 0), padx=10)

        # Progress info frame
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.pack(fill=tk.X, padx=5, pady=5)

        # Status label
        self.status_label = ttk.Label(
            progress_info_frame,
            text="Ready",
            font=("Arial", 10, "bold")
        )
        self.status_label.pack(side=tk.LEFT, padx=5)

        # Percentage label
        self.percentage_label = ttk.Label(
            progress_info_frame,
            text="0%",
            font=("Arial", 10, "bold")
        )
        self.percentage_label.pack(side=tk.RIGHT, padx=5)

        # Progress bar - make it more visible with increased height
        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(
            progress_frame,
            orient=tk.HORIZONTAL,
            length=100,
            mode='determinate',
            variable=self.progress_var,
            style="Thick.Horizontal.TProgressbar"
        )
        self.progress.pack(fill=tk.X, padx=5, pady=(0, 10))

    def browse_directory(self):
        """Open directory browser dialog and automatically scan the selected directory"""
        directory = filedialog.askdirectory(title="Select Directory")
        if directory:
            self.dir_var.set(directory)
            self.log_message(f"Selected directory: {directory}")
            # Set default output directory to be a subfolder of the selected directory
            default_output = os.path.join(directory, "converted_output")
            self.output_dir_var.set(default_output)
            # Automatically scan the directory after selection
            self.scan_directory()

    def browse_output_directory(self):
        """Open directory browser dialog for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir_var.set(directory)
            self.log_message(f"Selected output directory: {directory}")

    def scan_directory(self):
        """Scan the selected directory and populate the file tree"""
        directory = self.dir_var.get()
        if not directory or not os.path.isdir(directory):
            messagebox.showerror("Error", "Please select a valid directory")
            return

        # Clear existing items
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        self.selected_items.clear()

        # Configure tag appearance
        self.file_tree.tag_configure("folder", foreground="blue")
        self.file_tree.tag_configure("file", foreground="black")

        # Update status
        self.update_progress(0, "Scanning directory...")

        # Populate tree with files and folders
        self.log_message(f"Scanning directory: {directory}")
        self.populate_file_tree(directory)
        self.log_message("Scan complete")

        # Update status
        self.update_progress(100, "Scan complete")
        # Reset after a short delay
        self.root.after(1000, lambda: self.update_progress(0, "Ready"))

    def populate_file_tree(self, directory, parent=""):
        """Recursively populate the file tree with files and folders"""
        try:
            # Get all items in the directory
            items = os.listdir(directory)

            # Sort items (folders first, then files)
            folders = []
            files = []

            for item in items:
                full_path = os.path.join(directory, item)
                if os.path.isdir(full_path):
                    folders.append(item)
                else:
                    files.append(item)

            # Add folders first, then files
            for folder in sorted(folders):
                full_path = os.path.join(directory, folder)

                # Insert folder into tree
                folder_id = self.file_tree.insert(
                    parent,
                    "end",
                    values=(full_path, "Folder"),
                    text="☐",
                    tags=("folder",)
                )

                # Recursively add subfolders and files
                self.populate_file_tree(full_path, folder_id)

            for file in sorted(files):
                full_path = os.path.join(directory, file)

                # Skip files that shouldn't be processed
                if not self.should_process_file(full_path):
                    continue

                # Insert file into tree
                self.file_tree.insert(
                    parent,
                    "end",
                    values=(full_path, "File"),
                    text="☐",
                    tags=("file",)
                )

        except Exception as e:
            self.log_message(f"Error scanning {directory}: {str(e)}")

    def select_item_and_children(self, item_id, select=True):
        """Recursively select or deselect an item and all its children"""
        # Process the current item
        if select:
            self.file_tree.item(item_id, text="☑")
            self.selected_items.add(item_id)
        else:
            self.file_tree.item(item_id, text="☐")
            if item_id in self.selected_items:
                self.selected_items.remove(item_id)

        # Process all children recursively
        for child_id in self.file_tree.get_children(item_id):
            self.select_item_and_children(child_id, select)

    def on_tree_click(self, event):
        """Handle clicks on the file tree"""
        # Get the item that was clicked
        region = self.file_tree.identify_region(event.x, event.y)
        if region == "tree":
            # Get the item ID
            item_id = self.file_tree.identify_row(event.y)
            if item_id:
                # Toggle checkbox
                current_text = self.file_tree.item(item_id, "text")
                item_values = self.file_tree.item(item_id, "values")

                if current_text == "☐":
                    # Select this item and all its children
                    self.select_item_and_children(item_id, True)
                    if item_values:
                        self.log_message(f"Selected: {item_values[0]} (and all children)")
                else:
                    # Deselect this item and all its children
                    self.select_item_and_children(item_id, False)
                    if item_values:
                        self.log_message(f"Deselected: {item_values[0]} (and all children)")

    def on_tree_double_click(self, event):
        """Handle double-clicks on the file tree for expansion/collapse"""
        item_id = self.file_tree.identify_row(event.y)
        if item_id:
            # Check if it's a folder
            item_values = self.file_tree.item(item_id, "values")
            if item_values and item_values[1] == "Folder":
                # Toggle expand/collapse
                if self.file_tree.item(item_id, "open"):
                    self.file_tree.item(item_id, open=False)
                else:
                    self.file_tree.item(item_id, open=True)

    def select_all_items(self):
        """Select all items in the file tree"""
        for item_id in self.file_tree.get_children():
            self.select_item_and_children(item_id, True)
        self.log_message("Selected all items")

    def deselect_all_items(self):
        """Deselect all items in the file tree"""
        for item_id in self.file_tree.get_children():
            self.select_item_and_children(item_id, False)
        self.selected_items.clear()
        self.log_message("All items deselected")

    def submit_selection(self):
        """Submit the current selection"""
        selected_files = self.get_selected_files()
        if not selected_files:
            messagebox.showwarning("Warning", "No files selected. Please select files or folders to process.")
            return

        self.log_message(f"Selection submitted: {len(selected_files)} files selected")
        messagebox.showinfo("Selection Submitted", f"{len(selected_files)} files selected and ready for processing.")

    def copy_convert_zip_mode(self):
        """Copy selected files, convert extensions to .txt, and create a zip file"""
        # Check if source directory is selected
        source_dir = self.dir_var.get()
        if not source_dir or not os.path.isdir(source_dir):
            messagebox.showerror("Error", "Please select a valid source directory")
            return

        # Check if output directory is specified
        output_dir = self.output_dir_var.get()
        if not output_dir:
            messagebox.showerror("Error", "Please specify an output directory")
            return

        # Check if any files are selected
        if not self.selected_items:
            messagebox.showwarning("Warning", "No files or folders selected. Please select items to process.")
            return

        # Get selected files
        selected_files = self.get_selected_files()
        if not selected_files:
            messagebox.showwarning("Warning", "No files found in the selected items.")
            return

        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            self.log_message(f"Created output directory: {output_dir}")

            # Dictionary to store original extensions
            extension_map = {}

            # Update progress
            total_files = len(selected_files)
            self.log_message(f"Found {total_files} files to process")

            if total_files == 0:
                self.log_message("No files to process")
                return

            # Step 1: Copy files to output directory
            self.update_progress(0, "Copying files...")
            copied_files = []

            for i, filepath in enumerate(selected_files):
                try:
                    # Update progress
                    percentage = (i / total_files) * 33  # First third of the progress
                    self.update_progress(percentage, f"Copying file {i+1} of {total_files}")

                    # Check if source file exists
                    if not os.path.exists(filepath):
                        self.log_message(f"Warning: Source file not found: {filepath}")
                        continue

                    # Get relative path to maintain directory structure
                    rel_path = os.path.relpath(filepath, source_dir)
                    dest_path = os.path.join(output_dir, rel_path)

                    # Create destination directory if it doesn't exist
                    dest_dir = os.path.dirname(dest_path)
                    os.makedirs(dest_dir, exist_ok=True)

                    # Copy the file
                    shutil.copy2(filepath, dest_path)
                    copied_files.append(dest_path)
                    self.log_message(f"Copied: {filepath} -> {dest_path}")
                except Exception as e:
                    self.log_message(f"Error copying file {filepath}: {str(e)}")
                    # Continue with next file instead of aborting the whole process
                    continue

            # Step 2: Convert extensions to .txt
            self.update_progress(33, "Converting extensions...")

            for i, filepath in enumerate(copied_files):
                try:
                    # Update progress
                    percentage = 33 + (i / total_files) * 33  # Second third of the progress
                    self.update_progress(percentage, f"Converting file {i+1} of {total_files}")

                    # Check if file exists
                    if not os.path.exists(filepath):
                        self.log_message(f"Warning: File not found for conversion: {filepath}")
                        continue

                    filename, ext = os.path.splitext(filepath)
                    if ext.lower() != '.txt':
                        # Store original extension
                        relative_path = os.path.relpath(filepath, output_dir)
                        extension_map[relative_path] = ext

                        # Rename file to .txt
                        new_filepath = f"{filename}.txt"
                        os.rename(filepath, new_filepath)
                        self.log_message(f"Renamed: {filepath} -> {new_filepath}")
                except Exception as e:
                    self.log_message(f"Error converting file {filepath}: {str(e)}")
                    # Continue with next file instead of aborting the whole process
                    continue

            # Save extension map to JSON file
            map_path = os.path.join(output_dir, self.extension_map_file)
            with open(map_path, 'w') as f:
                json.dump(extension_map, f, indent=4)

            self.log_message(f"Extension map saved to {map_path}")

            # Step 3: Create zip file
            self.update_progress(66, "Creating zip file...")

            # Create a timestamp for the zip file name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            zip_filename = os.path.join(os.path.dirname(output_dir), f"airgap_package_{timestamp}.zip")

            try:
                # Make sure the extension map file exists before zipping
                if not os.path.exists(map_path):
                    self.log_message(f"Warning: Extension map file not found: {map_path}")

                # Get the output directory name (just the folder name, not the full path)
                output_dir_name = os.path.basename(output_dir)

                with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    file_count = 0

                    # 1. Create a README file with instructions
                    readme_content = (
                        "Air Gap Package Instructions\n\n"
                        "This package contains:\n"
                        "1. Converted files with .txt extensions in the 'converted_files' folder\n"
                        "2. The extension mapping file (extension_map.json) to track original extensions\n"
                        "3. The FileExtensionTool executable to restore original extensions\n\n"
                        "To restore original file extensions:\n"
                        "1. Extract this zip file\n"
                        "2. Run FileExtensionTool.exe\n"
                        "3. Browse to the 'converted_files' folder\n"
                        "4. Click 'Restore Original Extensions'\n"
                    )
                    zipf.writestr("README.txt", readme_content)
                    file_count += 1
                    self.log_message("Added README.txt to zip")

                    # 2. Create a folder for converted files
                    zipf.writestr("converted_files/", "")
                    self.log_message("Added converted_files/ directory to zip")

                    # 3. Add all files from the output directory to the converted_files folder
                    for root, dirs, files in os.walk(output_dir):
                        # Add empty directories
                        for dir_name in dirs:
                            dir_path = os.path.join(root, dir_name)
                            # Create a relative path for the directory
                            rel_path = os.path.relpath(dir_path, output_dir)
                            # Add directory to zip (with trailing slash)
                            zipf.writestr(f"converted_files/{rel_path}/", "")
                            self.log_message(f"Added directory to zip: converted_files/{rel_path}/")

                        # Add files
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                # Check if file exists
                                if not os.path.exists(file_path):
                                    self.log_message(f"Warning: File not found for zip: {file_path}")
                                    continue

                                # Get path relative to the output directory
                                rel_path = os.path.relpath(file_path, output_dir)
                                # Add to converted_files folder in zip
                                zipf.write(file_path, f"converted_files/{rel_path}")
                                file_count += 1
                                self.log_message(f"Added file to zip: converted_files/{rel_path}")
                            except Exception as e:
                                self.log_message(f"Error adding file to zip {file_path}: {str(e)}")
                                continue

                    # 4. Add the executable to the zip
                    try:
                        # Look for the executable in common locations
                        executable_paths = [
                            # Windows executable
                            os.path.join(os.path.dirname(os.path.abspath(__file__)), "dist", "FileExtensionTool.exe"),
                            # Linux executable
                            os.path.join(os.path.dirname(os.path.abspath(__file__)), "dist", "FileExtensionTool"),
                            # Current directory
                            os.path.join(os.getcwd(), "dist", "FileExtensionTool.exe"),
                            # Parent directory
                            os.path.join(os.path.dirname(os.getcwd()), "dist", "FileExtensionTool.exe")
                        ]

                        executable_found = False
                        for exe_path in executable_paths:
                            if os.path.exists(exe_path):
                                zipf.write(exe_path, "FileExtensionTool.exe")
                                file_count += 1
                                self.log_message(f"Added executable to zip: {exe_path} -> FileExtensionTool.exe")
                                executable_found = True
                                break

                        if not executable_found:
                            self.log_message("Warning: Could not find executable to include in the zip")
                            # Create a placeholder text file with instructions
                            exe_instructions = (
                                "The FileExtensionTool.exe could not be automatically included in this package.\n\n"
                                "Please manually copy the FileExtensionTool.exe to this folder before sending to the air-gapped environment.\n"
                            )
                            zipf.writestr("COPY_EXECUTABLE_HERE.txt", exe_instructions)
                            file_count += 1
                            self.log_message("Added executable instructions to zip")
                    except Exception as e:
                        self.log_message(f"Error adding executable to zip: {str(e)}")
                        # Create a placeholder with instructions
                        exe_instructions = (
                            "The FileExtensionTool.exe could not be automatically included in this package.\n\n"
                            "Please manually copy the FileExtensionTool.exe to this folder before sending to the air-gapped environment.\n"
                        )
                        zipf.writestr("COPY_EXECUTABLE_HERE.txt", exe_instructions)
                        file_count += 1
                        self.log_message("Added executable instructions to zip")

                self.log_message(f"Created zip file with {file_count} files: {zip_filename}")
            except Exception as e:
                self.log_message(f"Error creating zip file: {str(e)}")
                raise

            self.update_progress(100, "Completed")
            messagebox.showinfo("Success", f"Successfully processed {total_files} files.\n\nZip file created at:\n{zip_filename}")

        except Exception as e:
            self.log_message(f"Error in Copy/Convert/Zip mode: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            self.update_progress(0, "Ready")

    def reset_selection(self):
        """Reset the file tree and selection"""
        # Clear the file tree
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # Clear selected items
        self.selected_items.clear()

        # Clear status text
        self.status_text.delete(1.0, tk.END)

        # Reset progress
        self.update_progress(0, "Ready")

        self.log_message("Selection reset. Please select a directory and scan again.")

    def log_message(self, message):
        """Add message to status text and log file"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        logging.info(message)

    def update_progress(self, percentage, status_text="Processing"):
        """Update the progress bar and status labels"""
        # Update progress bar
        self.progress_var.set(percentage)

        # Update percentage label
        self.percentage_label.config(text=f"{int(percentage)}%")

        # Update status label
        self.status_label.config(text=status_text)

        # Force UI update
        self.root.update_idletasks()

    def should_process_file(self, filename):
        """Check if file should be processed"""
        # Get the base filename
        base_filename = os.path.basename(filename)

        # Skip files in ignore list, but only if they're in the root directory
        # This allows README.md files in subdirectories to be processed
        if base_filename in self.ignore_files:
            # Check if the file is in the root directory of the selected folder
            dir_var = self.dir_var.get() if hasattr(self, 'dir_var') else ''
            if os.path.dirname(filename) == dir_var:
                return False
            # Special case for README.md - allow it in subdirectories
            elif base_filename != "README.md":
                return False

        # Skip files with extensions in ignore list
        _, ext = os.path.splitext(filename)
        if ext.lower() in self.ignore_extensions:
            return False

        # Skip directories
        if os.path.isdir(filename):
            return False

        return True

    def get_selected_files(self) -> List[str]:
        """Get a list of selected files from the file tree"""
        selected_files = []
        processed_folders = set()  # To avoid duplicate processing

        # First pass: collect all selected folders to avoid duplicate processing
        for item_id in self.selected_items:
            item_values = self.file_tree.item(item_id, "values")
            if item_values:
                path = item_values[0]
                item_type = item_values[1]

                if item_type == "Folder":
                    processed_folders.add(path)
                    # Also add all parent folders to avoid duplicate processing
                    parent_path = os.path.dirname(path)
                    while parent_path and parent_path != os.path.dirname(parent_path):
                        processed_folders.add(parent_path)
                        parent_path = os.path.dirname(parent_path)

        # Second pass: process files and folders
        for item_id in self.selected_items:
            item_values = self.file_tree.item(item_id, "values")
            if item_values:
                path = item_values[0]
                item_type = item_values[1]

                # Skip if this file is in a folder we've already processed
                if item_type == "File":
                    parent_folder = os.path.dirname(path)
                    if parent_folder in processed_folders:
                        continue
                    selected_files.append(path)

                elif item_type == "Folder":
                    # Add all files in folder recursively
                    self.log_message(f"Processing folder: {path}")
                    for root, _, files in os.walk(path):
                        for file in files:
                            filepath = os.path.join(root, file)
                            if self.should_process_file(filepath):
                                selected_files.append(filepath)

        return selected_files

    def prep_mode(self):
        """Convert selected file extensions to .txt"""
        directory = self.dir_var.get()
        if not directory or not os.path.isdir(directory):
            messagebox.showerror("Error", "Please select a valid directory")
            return

        # Check if any files are selected
        if not self.selected_items:
            messagebox.showwarning("Warning", "No files or folders selected. Please select items to process.")
            return

        try:
            self.log_message(f"Starting Prep mode for selected files in: {directory}")

            # Dictionary to store original extensions
            extension_map = {}

            # Get list of selected files
            all_files = self.get_selected_files()

            # Update progress bar
            total_files = len(all_files)
            self.log_message(f"Found {total_files} files to process")

            if total_files == 0:
                self.log_message("No files to process")
                return

            # Process files
            for i, filepath in enumerate(all_files):
                # Update progress
                percentage = (i / total_files) * 100
                self.update_progress(percentage, f"Converting file {i+1} of {total_files}")

                filename, ext = os.path.splitext(filepath)
                if ext.lower() != '.txt':
                    # Store original extension
                    relative_path = os.path.relpath(filepath, directory)
                    extension_map[relative_path] = ext

                    # Rename file to .txt
                    new_filepath = f"{filename}.txt"
                    os.rename(filepath, new_filepath)
                    self.log_message(f"Renamed: {filepath} -> {new_filepath}")

            # Save extension map to JSON file
            map_path = os.path.join(directory, self.extension_map_file)
            with open(map_path, 'w') as f:
                json.dump(extension_map, f, indent=4)

            self.log_message(f"Extension map saved to {map_path}")
            self.update_progress(100, "Completed")
            messagebox.showinfo("Success", f"Successfully converted {total_files} files to .txt extension")

        except Exception as e:
            self.log_message(f"Error in Prep mode: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            self.update_progress(0, "Ready")

    def restore_mode(self):
        """Restore original file extensions"""
        directory = self.dir_var.get()
        if not directory or not os.path.isdir(directory):
            messagebox.showerror("Error", "Please select a valid directory")
            return

        map_path = os.path.join(directory, self.extension_map_file)
        if not os.path.exists(map_path):
            messagebox.showerror("Error", f"Extension map file not found: {map_path}")
            return

        # Check if any files are selected for selective restore
        selective_restore = False
        selected_paths = []

        if self.selected_items:
            selective_restore = True
            selected_paths = self.get_selected_files()
            if not selected_paths:
                messagebox.showwarning("Warning", "No files or folders selected for restoration.")
                return

        try:
            if selective_restore:
                self.log_message(f"Starting selective Restore mode for selected files in: {directory}")
            else:
                self.log_message(f"Starting Restore mode for all files in: {directory}")

            # Load extension map
            with open(map_path, 'r') as f:
                extension_map = json.load(f)

            # Filter extension map for selected files if doing selective restore
            if selective_restore:
                filtered_map = {}
                for rel_path, ext in extension_map.items():
                    full_path = os.path.join(directory, rel_path)
                    txt_path = os.path.splitext(full_path)[0] + ".txt"

                    # Check if this file or its parent directory is selected
                    for selected_path in selected_paths:
                        if txt_path == selected_path or txt_path.startswith(os.path.dirname(selected_path)):
                            filtered_map[rel_path] = ext
                            break

                extension_map = filtered_map

            total_files = len(extension_map)
            self.log_message(f"Found {total_files} files to restore")

            if total_files == 0:
                self.log_message("No files to restore")
                return

            # Process files
            restored_count = 0
            for i, (relative_path, original_ext) in enumerate(extension_map.items()):
                # Update progress
                percentage = (i / total_files) * 100
                self.update_progress(percentage, f"Restoring file {i+1} of {total_files}")

                # Get full path
                full_path = os.path.join(directory, relative_path)

                # Check if the .txt version exists
                txt_path = os.path.splitext(full_path)[0] + ".txt"
                if os.path.exists(txt_path):
                    # Restore original extension
                    original_path = os.path.splitext(txt_path)[0] + original_ext
                    os.rename(txt_path, original_path)
                    self.log_message(f"Restored: {txt_path} -> {original_path}")
                    restored_count += 1
                else:
                    self.log_message(f"Warning: File not found for restoration: {txt_path}")

            # Delete the extension map file
            os.remove(map_path)
            self.log_message(f"Deleted extension map file: {map_path}")

            self.update_progress(100, "Completed")
            messagebox.showinfo("Success", f"Successfully restored {restored_count} files to their original extensions")

        except Exception as e:
            self.log_message(f"Error in Restore mode: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            self.update_progress(0, "Ready")

def handle_exception(exc_type, exc_value, exc_traceback):
    """Global exception handler"""
    logging.error("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))
    messagebox.showerror("Error", f"An unexpected error occurred: {str(exc_value)}")

if __name__ == "__main__":
    # Set up global exception handler
    sys.excepthook = handle_exception

    # Create logs directory
    os.makedirs(log_directory, exist_ok=True)

    # Start the application
    root = tk.Tk()
    app = FileExtensionTool(root)
    root.mainloop()
