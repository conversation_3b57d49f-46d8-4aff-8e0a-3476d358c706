# Simple File Extension Renamer Tools

This folder contains simplified versions of the file extension renamer tool that should work on restricted systems.

## Available Versions

### 1. MinimalExtensionRenamer.bat

The simplest possible version with minimal dependencies:

- Works only in the current directory (where the script is located)
- No folder selection - just place it in the folder you want to process
- No subfolders - only processes files in the current directory
- Minimal error potential - uses only basic batch commands
- No external dependencies or temporary files

**How to use:**
1. Copy the script to the folder containing files you want to convert
2. Double-click to run it
3. Press C to convert extensions to .txt
4. Press R to restore original extensions (requires the extension_map.txt file)

### 2. BasicExtensionRenamer.bat

A slightly more advanced version with manual folder selection:

- Allows you to enter a folder path manually
- Option to process subfolders
- Text-based menu interface
- More detailed error messages
- No external dependencies or GUI components

**How to use:**
1. Double-click to run it
2. Select option 1 or 2 from the menu
3. Enter the full path of the folder to process
4. Choose whether to process subfolders (for conversion)
5. The script will process all files and create a mapping file

## Troubleshooting

If you're getting "The system cannot find the path specified" errors:

1. Try the MinimalExtensionRenamer.bat first - it's the simplest and most likely to work
2. Make sure you're running the script on a Windows system
3. Check that you have permission to write to the folder where the script is located
4. Avoid paths with special characters or very long paths
5. If using BasicExtensionRenamer.bat, make sure to enter the full path correctly (e.g., C:\MyFiles\Documents)

## Notes

- Both scripts create an extension_map.txt file to track original extensions
- This file is needed for restoring the original extensions later
- Files that already have a .txt extension are skipped during conversion
- The scripts themselves and the map file are excluded from processing
