# NetBackup bp.conf configuration tasks
# Enhanced with proper error handling and validation

- name: Log NetBackup configuration start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting NetBackup bp.conf configuration"
  delegate_to: localhost

- name: Check if NetBackup directory exists
  ansible.builtin.stat:
    path: /usr/openv/netbackup
  register: netbackup_dir

- name: Create NetBackup directory if it doesn't exist
  ansible.builtin.file:
    path: /usr/openv/netbackup
    state: directory
    mode: '0755'
    owner: root
    group: root
  when: not netbackup_dir.stat.exists

- name: Backup existing bp.conf if it exists
  ansible.builtin.copy:
    src: /usr/openv/netbackup/bp.conf
    dest: "/usr/openv/netbackup/bp.conf.backup.{{ ansible_date_time.epoch }}"
    remote_src: true
    mode: '0644'
  when: netbackup_dir.stat.exists
  ignore_errors: true

- name: Configure NetBackup bp.conf
  ansible.builtin.script: "{{ role_path }}/files/set-bpconf.py"
  register: netbackup_result
  failed_when: false

- name: Handle NetBackup configuration failure
  ansible.builtin.fail:
    msg: "NetBackup configuration failed: {{ netbackup_result.stderr | default('Unknown error') }}"
  when: 
    - netbackup_result.rc != 0
    - not (ignore_configuration_errors | default(false))

- name: Log NetBackup configuration result
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - NetBackup configuration {{ 'completed' if netbackup_result.rc == 0 else 'failed' }}"
  delegate_to: localhost

- name: Debug NetBackup configuration output
  ansible.builtin.debug:
    var: netbackup_result.stdout_lines
  when: debug_output | default(false)

- name: Verify bp.conf was created
  ansible.builtin.stat:
    path: /usr/openv/netbackup/bp.conf
  register: bp_conf_file

- name: Log bp.conf verification
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - bp.conf file {{ 'exists' if bp_conf_file.stat.exists else 'missing' }}"
  delegate_to: localhost
