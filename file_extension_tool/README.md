# File Extension Tool for Air Gap Solutions

A comprehensive GUI tool to prepare files for transfer to air-gapped environments by converting file extensions to `.txt`, creating a complete package with the necessary tools for restoration, and restoring files back to their original extensions.

**Author:** <PERSON> - CES Operational Excellence Team

## Available Tools

1. **Python GUI Tool** (`extension_tool.py`) - Full-featured tool with advanced capabilities
2. **PowerShell GUI Tool** (`FileExtensionRenamer.ps1`) - Cross-platform alternative that works on Windows and Linux
3. **Batch Script** (`FileExtensionRenamer.bat`) - Simple Windows-only alternative

## Features

- User-friendly GUI interface with enhanced progress tracking
- Three operation modes:
  - **Copy, Convert & Zip for Email**: Creates a complete package for air-gapped environments
  - **Convert to .txt (In Place)**: Converts file extensions to `.txt` in the original location
  - **Restore Original Extensions**: Restores files to their original extensions
- Recursive folder selection:
  - Selecting a parent folder automatically selects all children
  - Process entire directory structures with a single click
- Complete air gap solution:
  - Creates a zip file with converted files, mapping data, and the restoration tool
  - Self-contained package that can be emailed to air-gapped environments
  - Includes the executable for Windows systems without Python installed
- Robust error handling and detailed logging
- Cross-platform support for Windows and Linux

## Usage

### Air Gap Workflow (Recommended)

#### Preparing Files for Air-Gapped Environment

1. Download the `FileExtensionTool.exe` (Windows) or `FileExtensionTool` (Linux) from the `dist` folder
2. Run the application
3. Use the "Browse" button to select a source directory
4. The file tree will automatically populate
5. Select the folders/files you want to process (selecting a folder automatically selects all its contents)
6. Specify an output directory or use the default
7. Click "Copy, Convert & Zip for Email"
8. The tool will:
   - Copy selected files to the output directory
   - Convert all extensions to `.txt`
   - Create a zip file containing:
     - The converted files
     - The extension mapping file
     - The executable tool itself
     - A README with instructions
9. Email the resulting zip file to the air-gapped environment

#### Restoring Files in Air-Gapped Environment

1. Extract the zip file received via email
2. Run the included `FileExtensionTool.exe`
3. Use the "Browse" button to select the extracted `converted_files` folder
4. Click "Restore Original Extensions"
5. All files will be restored to their original extensions

### Alternative Workflows

#### Direct Conversion (In Place)

1. Run the application
2. Select a directory and files/folders to process
3. Click "Convert to .txt (In Place)" to convert extensions directly
4. To restore, select the same directory and click "Restore Original Extensions"

### Building from Source

#### Prerequisites

- Python 3.6 or higher
- PyInstaller (`pip install pyinstaller`)

#### Build Steps

##### Windows

1. Clone or download this repository
2. Open a command prompt in the project directory
3. Run the Windows build script:
   ```
   build.bat
   ```
4. The executable will be created in the `dist` folder

##### Linux

1. Clone or download this repository
2. Open a terminal in the project directory
3. Make the build script executable if needed:
   ```
   chmod +x build.sh
   ```
4. Run the Linux build script:
   ```
   ./build.sh
   ```
5. The executable will be created in the `dist` folder

## How It Works

### Copy, Convert & Zip Mode

1. Copies selected files/folders to the output directory (preserving structure)
2. Converts all file extensions to `.txt` in the copied files
3. Creates a JSON map file that records the original extension of each file
4. Packages everything into a zip file with:
   - A `converted_files` folder containing all converted files
   - The extension mapping file inside the converted_files folder
   - The executable tool for restoration
   - A README with instructions
5. Logs all operations to a log file

### Convert to .txt Mode (In Place)

1. Creates a JSON map file that records the original extension of each selected file
2. Renames selected files to have a `.txt` extension in their original location
3. Logs all operations to a log file

### Restore Mode

1. Reads the JSON map file to identify original file extensions
2. Allows selective restoration if specific files are selected
3. Renames `.txt` files back to their original extensions
4. Deletes the JSON map file after restoration is complete
5. Logs all operations to a log file

### Folder Selection

1. When you select a folder, all its subfolders and files are automatically selected
2. This makes it easy to process entire directory structures
3. You can still deselect specific files or subfolders if needed

## UI Elements

### Main Controls

- **Directory Selection**: Browse and select the source directory
- **Output Directory**: Specify where copied files should be placed
- **File Tree**: View and select files/folders to process
- **Selection Buttons**: Select All, Deselect All, Submit Selection, Reset
- **Action Buttons**: Copy/Convert/Zip, Convert In-Place, Restore
- **Progress Bar**: Shows operation progress with percentage and status

### Progress Tracking

- **Status Messages**: Shows the current operation (e.g., "Copying file 3 of 10")
- **Percentage Display**: Shows exact completion percentage
- **Log Window**: Displays detailed operation logs

## Notes

- The tool ignores certain files and extensions by default:
  - The extension map file itself
  - The Python script and executable
  - README.md files in the root directory (README.md files in subdirectories are processed)
  - Files with `.txt`, `.log`, `.json`, `.exe`, and `.spec` extensions
- Log files are stored in a `logs` directory within the tool's directory
- The zip file is created with a timestamp in the name for easy identification

## Troubleshooting

If you encounter any issues:

1. Check the log files in the `logs` directory
2. Ensure you have proper permissions to modify files in the selected directory
3. Make sure the extension map file (`extension_map.json`) is present when restoring
4. If the executable is not included in the zip, manually copy it before sending
5. For large directories, be patient as scanning and processing may take time

## PowerShell Tool Usage

### On Windows

1. Right-click on `FileExtensionRenamer.ps1` and select "Run with PowerShell"
   - If you get a security warning, you may need to run: `Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process`

### On Linux

1. Make sure PowerShell Core is installed:
   ```
   sudo apt-get install -y powershell  # For Ubuntu/Debian
   ```
   or
   ```
   sudo dnf install powershell  # For Fedora/RHEL
   ```

2. Run the helper script:
   ```
   ./run_renamer.sh
   ```

### Using the PowerShell GUI

1. In the GUI:
   - Select "Single File" or "Folder" as your source type
   - Click "Browse" to select the source file or folder
   - Choose "Convert to .txt" or "Restore original extensions" as the operation
   - Check "Process subfolders" if you want to include subfolders (for folder operations)
   - Click "Execute" to start the operation

The PowerShell tool provides a simpler interface but includes all the essential functionality for converting file extensions to .txt and restoring them.
