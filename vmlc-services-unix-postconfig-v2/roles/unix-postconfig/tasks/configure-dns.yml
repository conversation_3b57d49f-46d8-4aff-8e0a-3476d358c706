# DNS resolv.conf configuration tasks
# Enhanced with proper error handling and validation

- name: Log DNS configuration start
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - Starting DNS configuration for {{ service_fqdn }} at {{ service_location }}"
  delegate_to: localhost

- name: Validate DNS parameters
  ansible.builtin.assert:
    that:
      - service_fqdn != ""
      - service_location in ['HDC1', 'HDC2']
    fail_msg: "DNS parameters invalid. FQDN: {{ service_fqdn }}, Location: {{ service_location }}"
    success_msg: "DNS parameters validated successfully"

- name: Check if resolv.conf exists
  ansible.builtin.stat:
    path: /etc/resolv.conf
  register: resolv_conf_file

- name: Backup existing resolv.conf
  ansible.builtin.copy:
    src: /etc/resolv.conf
    dest: "/etc/resolv.conf.backup.{{ ansible_date_time.epoch }}"
    remote_src: true
    mode: '0644'
  when: resolv_conf_file.stat.exists

- name: Configure resolv.conf
  ansible.builtin.script: "{{ role_path }}/files/set-resolv.py {{ service_fqdn }} {{ service_location }}"
  register: dns_result
  failed_when: false

- name: Handle DNS configuration failure
  ansible.builtin.fail:
    msg: "DNS configuration failed: {{ dns_result.stderr | default('Unknown error') }}"
  when: 
    - dns_result.rc != 0
    - not (ignore_configuration_errors | default(false))

- name: Log DNS configuration result
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - DNS configuration {{ 'completed' if dns_result.rc == 0 else 'failed' }} for {{ service_fqdn }}"
  delegate_to: localhost

- name: Debug DNS configuration output
  ansible.builtin.debug:
    var: dns_result.stdout_lines
  when: debug_output | default(false)

- name: Test DNS resolution
  ansible.builtin.command: nslookup {{ service_fqdn }}
  register: dns_test
  failed_when: false
  changed_when: false

- name: Log DNS resolution test
  ansible.builtin.lineinfile:
    path: "{{ log_file | default('/var/log/ansible-postconfig.log') }}"
    line: "{{ ansible_date_time.iso8601 }} - DNS resolution test for {{ service_fqdn }}: {{ 'successful' if dns_test.rc == 0 else 'failed' }}"
  delegate_to: localhost
