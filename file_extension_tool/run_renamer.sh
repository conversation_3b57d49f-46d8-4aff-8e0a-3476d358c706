#!/bin/bash
# Run the File Extension Renamer Tool
# This script checks if PowerShell Core is installed and runs the PowerShell script

# Make the PowerShell script executable
chmod +x FileExtensionRenamer.ps1

# Check if PowerShell Core is installed
if command -v pwsh &> /dev/null; then
    echo "Running File Extension Renamer Tool..."
    pwsh -File ./FileExtensionRenamer.ps1
else
    echo "Error: PowerShell Core (pwsh) is not installed."
    echo "Please install PowerShell Core to use this tool."
    echo ""
    echo "Installation instructions:"
    echo ""
    echo "For Ubuntu/Debian:"
    echo "  sudo apt-get update"
    echo "  sudo apt-get install -y wget apt-transport-https software-properties-common"
    echo "  wget -q https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/packages-microsoft-prod.deb"
    echo "  sudo dpkg -i packages-microsoft-prod.deb"
    echo "  sudo apt-get update"
    echo "  sudo apt-get install -y powershell"
    echo ""
    echo "For Fedora/RHEL:"
    echo "  sudo dnf install -y powershell"
    echo ""
    echo "For more information, visit:"
    echo "https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell-on-linux"
    exit 1
fi
